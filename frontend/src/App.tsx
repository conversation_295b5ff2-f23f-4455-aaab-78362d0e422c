import { BrowserRouter, Routes, Route } from "react-router-dom";
import ConnectionPage from "./pages/ConnectionPage";
import PressManagementPage from "./pages/PressManagementPage";

export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<ConnectionPage />} />
        <Route path="/press-management" element={<PressManagementPage />} />
      </Routes>
    </BrowserRouter>
  );
}
