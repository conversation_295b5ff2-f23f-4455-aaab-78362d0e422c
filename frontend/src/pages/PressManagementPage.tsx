import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// 导入组件
import Header from "../components/shared/Header";
import TabNavigation from "../components/shared/TabNavigation";
import type { TabType } from "../components/shared/TabNavigation";
import UpgradeManager from "../components/press/upgrade/UpgradeManager";
import ConfigManager from "../components/press/config/ConfigManager";
import NetworkManager from "../components/press/network/NetworkManager";
import LogManager from "../components/press/log/LogManager";
import FileManager from "../components/press/file/FileManager";
import Terminal from "../components/press/terminal/Terminal";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

export default function PressManagementPage() {
  const navigate = useNavigate();
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>("upgrade");
  const [isValidating, setIsValidating] = useState(true);

  // 断开连接
  const handleDisconnect = async () => {
    if (!sessionInfo) return;

    try {
      await fetch(`http://127.0.0.1:8000/api/ssh/sessions/${sessionInfo.sessionId}`, {
        method: "DELETE"
      });
    } catch (error) {
      console.error("断开连接失败:", error);
    } finally {
      localStorage.removeItem("ssh_session");
      navigate("/");
    }
  };

  // 验证会话有效性
  useEffect(() => {
    const validateSession = async () => {
      try {
        const sessionData = localStorage.getItem("ssh_session");
        if (!sessionData) {
          navigate("/");
          return;
        }
        const session = JSON.parse(sessionData) as SessionInfo;

        // 验证会话是否仍然有效
        const response = await fetch(`http://127.0.0.1:8000/api/ssh/sessions/${session.sessionId}`);
        if (!response.ok) {
          localStorage.removeItem("ssh_session");
          navigate("/");
          return;
        }

        setSessionInfo(session);
      } catch (error) {
        console.error("会话验证失败:", error);
        navigate("/");
      } finally {
        setIsValidating(false);
      }
    };

    validateSession();
  }, [navigate]);

  // 渲染标签页内容
  const renderTabContent = () => {
    if (!sessionInfo) return null;

    switch (activeTab) {
      case "upgrade":
        return <UpgradeManager sessionInfo={sessionInfo} />;
      case "config":
        return <ConfigManager sessionInfo={sessionInfo} />;
      case "network":
        return <NetworkManager sessionInfo={sessionInfo} />;
      case "logs":
        return <LogManager sessionInfo={sessionInfo} />;
      case "files":
        return <FileManager sessionInfo={sessionInfo} />;
      case "terminal":
        return <Terminal sessionInfo={sessionInfo} />;
      default:
        return (
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
            <p className="text-gray-600">选择一个标签页开始使用</p>
          </div>
        );
    }
  };

  // 如果正在验证会话，显示加载界面
  if (isValidating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
          <div className="flex flex-col items-center gap-4">
            <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <p className="text-gray-600">验证会话中...</p>
          </div>
        </div>
      </div>
    );
  }

  // 如果会话信息不存在，返回空（会被重定向处理）
  if (!sessionInfo) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* 页面头部 */}
        <Header sessionInfo={sessionInfo} onDisconnect={handleDisconnect} />

        {/* 标签页导航 */}
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />

        {/* 标签页内容 */}
        {renderTabContent()}
      </div>
    </div>
  );
}
