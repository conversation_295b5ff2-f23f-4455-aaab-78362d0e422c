import { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function ConnectionPage() {
  const navigate = useNavigate();
  const [productLine, setProductLine] = useState<"press" | "tighten">("press");
  const [connectionForm, setConnectionForm] = useState({
    host: "**************",
    port: "22",
    username: "root",
    password: "",
    timeout: "10"
  });
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "connecting" | "connected" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState("");

  const handleConnect = async () => {
    setConnectionStatus("connecting");
    setErrorMessage("");

    try {
      const response = await fetch("http://127.0.0.1:8000/api/ssh/connect", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          host: connectionForm.host,
          port: parseInt(connectionForm.port),
          username: connectionForm.username,
          password: connectionForm.password,
          timeout: parseInt(connectionForm.timeout),
          product_line: productLine
        })
      });

      const data = await response.json();

      if (response.ok) {
        setConnectionStatus("connected");

        // 保存会话信息到localStorage
        localStorage.setItem("ssh_session", JSON.stringify({
          sessionId: data.session_id,
          host: connectionForm.host,
          username: connectionForm.username,
          productLine: productLine,
          connectedAt: new Date().toISOString()
        }));

        // 跳转到管理界面
        setTimeout(() => {
          navigate("/press-management");
        }, 1500);
      } else {
        setConnectionStatus("error");
        setErrorMessage(data.detail || "连接失败");
      }
    } catch (error) {
      setConnectionStatus("error");
      setErrorMessage("网络错误，请检查后端服务");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-6">
      {/* Mac 风格主卡片 */}
      <div className="w-full max-w-md">
        {/* 产品线选择 */}
        <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 text-center">选择产品线</h2>
          <div className="flex gap-3">
            <button
              onClick={() => setProductLine("press")}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center gap-2 ${
                productLine === "press"
                  ? "bg-blue-500 text-white shadow-lg scale-105"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              <img src="/img/press.png" alt="压机" className="w-5 h-auto" />
              压机设备
            </button>
            <button
              onClick={() => setProductLine("tighten")}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all ${
                productLine === "tighten"
                  ? "bg-green-500 text-white shadow-lg scale-105"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              }`}
            >
              🌀 拧紧设备
            </button>
          </div>
        </div>

        {/* 连接表单 */}
        <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
          <div className="text-center mb-8">
            <div className="text-4xl mb-3 flex justify-center">
              {productLine === "press" ? (
                <img src="/img/press.png" alt="压机" className="w-12 h-auto" />
              ) : (
                "🌀"
              )}
            </div>
            <h1 className="text-2xl font-bold text-gray-800">
              {productLine === "press" ? "压机设备" : "拧紧设备"}远程连接
            </h1>
            <p className="text-gray-500 mt-2">请填写设备连接信息</p>
          </div>

          {/* 连接状态显示 */}
          {connectionStatus === "connecting" && (
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6 text-center">
              <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-blue-700">正在连接 {connectionForm.host}...</p>
            </div>
          )}

          {connectionStatus === "connected" && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6 text-center">
              <div className="text-green-600 text-2xl mb-2">✅</div>
              <p className="text-green-700 font-medium">连接成功！</p>
              <p className="text-green-600 text-sm">正在跳转到管理界面...</p>
            </div>
          )}

          {connectionStatus === "error" && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 text-center">
              <div className="text-red-600 text-2xl mb-2">❌</div>
              <p className="text-red-700 font-medium">连接失败</p>
              <p className="text-red-600 text-sm">{errorMessage}</p>
            </div>
          )}

          {/* 表单输入 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">设备IP地址</label>
              <input
                type="text"
                value={connectionForm.host}
                onChange={(e) => setConnectionForm({...connectionForm, host: e.target.value})}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white/50"
                placeholder="**************"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">SSH端口</label>
                <input
                  type="text"
                  value={connectionForm.port}
                  onChange={(e) => setConnectionForm({...connectionForm, port: e.target.value})}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white/50"
                  placeholder="22"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">超时(秒)</label>
                <input
                  type="text"
                  value={connectionForm.timeout}
                  onChange={(e) => setConnectionForm({...connectionForm, timeout: e.target.value})}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white/50"
                  placeholder="10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
              <input
                type="text"
                value={connectionForm.username}
                onChange={(e) => setConnectionForm({...connectionForm, username: e.target.value})}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white/50"
                placeholder="root"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                type="password"
                value={connectionForm.password}
                onChange={(e) => setConnectionForm({...connectionForm, password: e.target.value})}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white/50"
                placeholder="请输入密码"
              />
            </div>
          </div>

          {/* 连接按钮 */}
          <button
            onClick={handleConnect}
            disabled={connectionStatus === "connecting"}
            className="w-full mt-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:scale-105 disabled:opacity-50 disabled:scale-100 transition-all duration-200"
          >
            {connectionStatus === "connecting" ? "连接中..." : "🔗 连接设备"}
          </button>

          {/* 快速连接 */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500 mb-3">💡 常用设备快速连接:</p>
            <div className="flex gap-2">
              <button
                onClick={() => setConnectionForm({...connectionForm, host: "**************"})}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-600 transition-colors"
              >
                **************
              </button>
              <button
                onClick={() => setConnectionForm({...connectionForm, host: "**********"})}
                className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-600 transition-colors"
              >
                **********
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
