import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 升级类型
type UpgradeType = "PressControl" | "Database";

// 升级状态接口
interface UpgradeStatus {
  is_upgrading: boolean;
  current_step: string;
  progress: number;
  steps: Array<{
    step_name: string;
    is_completed: boolean;
    message: string;
    timestamp: string;
  }>;
  log_messages: string[];
  start_time?: string;
  end_time?: string;
  success?: boolean;
}

// 备份信息接口
interface BackupInfo {
  backup_name: string;
  type: UpgradeType;
  backup_time: string;
  backup_size: number;
  backup_path: string;
  display_name: string;
}

interface UpgradeManagerProps {
  sessionInfo: SessionInfo;
}

export default function UpgradeManager({ sessionInfo }: UpgradeManagerProps) {
  const [upgradeStatus, setUpgradeStatus] = useState<UpgradeStatus | null>(null);
  const [backupList, setBackupList] = useState<BackupInfo[]>([]);
  const [_isLoadingUpgrade, setIsLoadingUpgrade] = useState(false);
  const [_upgradeFiles, setUpgradeFiles] = useState<{
    pressControl: File | null;
    database: File | null;
  }>({ pressControl: null, database: null });
  const [validationResults, setValidationResults] = useState<{
    pressControl: { success: boolean; message: string } | null;
    database: { success: boolean; message: string } | null;
  }>({ pressControl: null, database: null });

  // 选择升级文件
  const handleUpgradeFileSelect = (type: UpgradeType, file: File | null) => {
    setUpgradeFiles(prev => ({
      ...prev,
      [type === "PressControl" ? "pressControl" : "database"]: file
    }));

    // 清除之前的验证结果
    setValidationResults(prev => ({
      ...prev,
      [type === "PressControl" ? "pressControl" : "database"]: null
    }));
  };

  // 上传并验证升级包
  const uploadAndValidatePackage = async (type: UpgradeType, file: File) => {
    setIsLoadingUpgrade(true);
    try {
      // 1. 上传文件
      const formData = new FormData();
      formData.append('file', file);
      formData.append('session_id', sessionInfo.sessionId);
      formData.append('upgrade_type', type);

      const uploadResponse = await fetch('http://127.0.0.1:8000/api/press/upgrade/upload', {
        method: 'POST',
        body: formData
      });

      const uploadData = await uploadResponse.json();

      if (!uploadResponse.ok || !uploadData.success) {
        throw new Error(uploadData.message || `上传失败: ${uploadResponse.status}`);
      }

      // 2. 验证文件
      const validateResponse = await fetch('http://127.0.0.1:8000/api/press/upgrade/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          type: type,
          package_path: uploadData.package_path
        })
      });

      const validateData = await validateResponse.json();

      if (!validateResponse.ok) {
        throw new Error(`验证失败: ${validateResponse.status}`);
      }

      // 更新验证结果
      setValidationResults(prev => ({
        ...prev,
        [type === "PressControl" ? "pressControl" : "database"]: {
          success: validateData.success,
          message: validateData.message
        }
      }));

      alert(validateData.success ? `${type}升级包验证成功!` : `验证失败: ${validateData.message}`);

    } catch (error) {
      console.error('上传验证失败:', error);
      alert(`上传验证失败: ${error}`);
      setValidationResults(prev => ({
        ...prev,
        [type === "PressControl" ? "pressControl" : "database"]: {
          success: false,
          message: `上传验证失败: ${error}`
        }
      }));
    } finally {
      setIsLoadingUpgrade(false);
    }
  };

  // 执行升级
  const executeUpgrade = async (type: UpgradeType) => {
    if (!confirm(`确定要执行${type}升级吗？此操作将会停止服务并替换文件。`)) return;

    try {
      const response = await fetch('http://127.0.0.1:8000/api/press/upgrade/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          type: type
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `升级失败: ${response.status}`);
      }

      alert(`${type}升级任务已启动!`);
      // 开始轮询升级状态
      startPollingUpgradeStatus();

    } catch (error) {
      console.error('升级失败:', error);
      alert(`升级失败: ${error}`);
    }
  };

  // 轮询升级状态
  const startPollingUpgradeStatus = () => {
    console.log('开始轮询升级状态...');

    const pollStatus = async () => {
      try {
        console.log('查询升级状态:', sessionInfo.sessionId);
        const response = await fetch(`http://127.0.0.1:8000/api/press/upgrade/status/${sessionInfo.sessionId}`);
        const status = await response.json();

        console.log('升级状态:', status);
        setUpgradeStatus(status);

        // 如果升级还在进行中，继续轮询
        if (status.is_upgrading) {
          console.log(`升级进行中: ${status.current_step}, 进度: ${status.progress}%`);
          setTimeout(pollStatus, 2000); // 每2秒查询一次
        } else {
          console.log('升级任务结束:', status.success ? '成功' : '失败');
        }
      } catch (error) {
        console.error('获取升级状态失败:', error);
        // 即使出错也要继续轮询一段时间
        setTimeout(pollStatus, 3000);
      }
    };

    // 立即查询一次，然后开始轮询
    pollStatus();
  };

  // 加载备份列表
  const loadBackupList = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/press/upgrade/backup/list/${sessionInfo.sessionId}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setBackupList(data.backups);
      } else {
        console.error('加载备份列表失败:', data.message);
      }
    } catch (error) {
      console.error('加载备份列表失败:', error);
    }
  };

  // 恢复备份
  const restoreBackup = async (backupName: string) => {
    if (!confirm(`确定要恢复到版本 ${backupName} 吗？此操作将停止服务并替换当前文件。`)) return;

    try {
      const response = await fetch('http://127.0.0.1:8000/api/press/upgrade/backup/restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          backup_name: backupName
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `恢复失败: ${response.status}`);
      }

      alert(`恢复成功: ${data.message}`);
      loadBackupList(); // 刷新备份列表

    } catch (error) {
      console.error('恢复备份失败:', error);
      alert(`恢复备份失败: ${error}`);
    }
  };

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
  };

  // 页面加载时获取备份列表
  useEffect(() => {
    if (sessionInfo) {
      loadBackupList();
    }
  }, [sessionInfo]);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">系统升级</h2>
        <button
          onClick={loadBackupList}
          className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
        >
          🔄 刷新列表
        </button>
      </div>

      {/* 升级状态显示 */}
      {upgradeStatus && upgradeStatus.is_upgrading && (
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-4 mb-6">
          <h3 className="font-medium text-orange-800 mb-2">升级进行中: {upgradeStatus.current_step}</h3>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
            <div
              className="bg-orange-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${upgradeStatus.progress}%` }}
            ></div>
          </div>
          <div className="text-sm text-orange-600">
            {upgradeStatus.progress}% 完成
          </div>
          <div className="mt-2 max-h-20 overflow-y-auto text-xs text-gray-600">
            {upgradeStatus.log_messages.slice(-3).map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-2 gap-8">
        {/* 下位机系统列 */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-blue-700 mb-4 flex items-center gap-2">
            🧠 下位机系统 (PressControl)
          </h3>

          {/* 下位机升级 */}
          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200/30">
            <h4 className="font-medium text-blue-700 mb-3 flex items-center gap-2">
              🚀 升级操作
            </h4>
            <div className="space-y-2">
              <label className="block">
                <input
                  type="file"
                  accept=".zip"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    handleUpgradeFileSelect("PressControl", file);
                    if (file) uploadAndValidatePackage("PressControl", file);
                  }}
                  className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:bg-blue-500 file:text-white hover:file:bg-blue-600"
                />
              </label>
              {validationResults.pressControl && (
                <div className={`text-sm p-2 rounded-lg ${
                  validationResults.pressControl.success
                    ? 'bg-green-100 text-green-700'
                    : 'bg-red-100 text-red-700'
                }`}>
                  {validationResults.pressControl.message}
                </div>
              )}
              <button
                onClick={() => executeUpgrade("PressControl")}
                disabled={!validationResults.pressControl?.success || upgradeStatus?.is_upgrading}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                执行升级
              </button>
            </div>
          </div>

          {/* 下位机备份 */}
          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200/30">
            <h4 className="font-medium text-blue-700 mb-3 flex items-center gap-2">
              💾 备份管理
            </h4>
            <div className="space-y-3 max-h-47 overflow-y-auto">
              {backupList.filter(backup => backup.type === 'PressControl').length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-blue-400 mb-2 text-2xl">📦</div>
                  <p className="text-sm text-blue-600">暂无备份</p>
                </div>
              ) : (
                backupList
                  .filter(backup => backup.type === 'PressControl')
                  .map((backup, index) => (
                    <div key={index} className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-blue-200/50 shadow-sm hover:shadow-md transition-all">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="font-medium text-blue-800 text-sm">PressControl</span>
                          </div>
                          <div className="text-xs text-gray-600 mb-1">{backup.backup_time}</div>
                          <div className="text-xs text-gray-500">
                            {formatBytes(backup.backup_size)}
                          </div>
                        </div>
                        <button
                          onClick={() => restoreBackup(backup.backup_name)}
                          disabled={upgradeStatus?.is_upgrading}
                          className="px-3 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 text-xs font-medium transition-colors shadow-sm"
                        >
                          恢复
                        </button>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>
        </div>

        {/* 数据库系统列 */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-green-700 mb-4 flex items-center gap-2">
            🗄️ 数据库系统 (Database)
          </h3>

          {/* 数据库升级 */}
          <div className="bg-green-50 rounded-xl p-6 border border-green-200/30">
            <h4 className="font-medium text-green-700 mb-3 flex items-center gap-2">
              🚀 升级操作
            </h4>
            <div className="space-y-2">
              <label className="block">
                <input
                  type="file"
                  accept=".zip"
                  onChange={(e) => {
                    const file = e.target.files?.[0] || null;
                    handleUpgradeFileSelect("Database", file);
                    if (file) uploadAndValidatePackage("Database", file);
                  }}
                  className="w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:bg-green-500 file:text-white hover:file:bg-green-600"
                />
              </label>
              {validationResults.database && (
                <div className={`text-sm p-2 rounded-lg ${
                  validationResults.database.success
                    ? 'bg-green-100 text-green-700'
                    : 'bg-red-100 text-red-700'
                }`}>
                  {validationResults.database.message}
                </div>
              )}
              <button
                onClick={() => executeUpgrade("Database")}
                disabled={!validationResults.database?.success || upgradeStatus?.is_upgrading}
                className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                执行升级
              </button>
            </div>
          </div>

          {/* 数据库备份 */}
          <div className="bg-green-50 rounded-xl p-6 border border-green-200/30">
            <h4 className="font-medium text-green-700 mb-3 flex items-center gap-2">
              💾 备份管理
            </h4>
            <div className="space-y-3 max-h-47 overflow-y-auto">
              {backupList.filter(backup => backup.type === 'Database').length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-green-400 mb-2 text-2xl">🗄️</div>
                  <p className="text-sm text-green-600">暂无备份</p>
                </div>
              ) : (
                backupList
                  .filter(backup => backup.type === 'Database')
                  .map((backup, index) => (
                    <div key={index} className="bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-green-200/50 shadow-sm hover:shadow-md transition-all">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="font-medium text-green-800 text-sm">Database</span>
                          </div>
                          <div className="text-xs text-gray-600 mb-1">{backup.backup_time}</div>
                          <div className="text-xs text-gray-500">
                            {formatBytes(backup.backup_size)}
                          </div>
                        </div>
                        <button
                          onClick={() => restoreBackup(backup.backup_name)}
                          disabled={upgradeStatus?.is_upgrading}
                          className="px-3 py-1.5 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 text-xs font-medium transition-colors shadow-sm"
                        >
                          恢复
                        </button>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
