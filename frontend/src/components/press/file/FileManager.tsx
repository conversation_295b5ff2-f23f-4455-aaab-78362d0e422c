import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 文件信息类型
interface FileInfo {
  name: string;
  type: "file" | "directory";
  size: number;
  permissions: string;
  owner: string;
  group: string;
  modified_time: string;
  path: string;
}

interface FileManagerProps {
  sessionInfo: SessionInfo;
}

export default function FileManager({ sessionInfo }: FileManagerProps) {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [currentPath, setCurrentPath] = useState("/root");
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);

  // 下载进度状态
  const [downloadProgress, setDownloadProgress] = useState<{
    fileName: string;
    progress: number;
    isDownloading: boolean;
  } | null>(null);

  // 加载文件列表
  const loadFiles = async (path: string) => {
    setIsLoadingFiles(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/file/list", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          path: path
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      setFiles(data.files);
      setCurrentPath(data.current_path);
    } catch (error) {
      console.error("加载文件列表失败:", error);
      alert("加载文件列表失败: " + error);
    } finally {
      setIsLoadingFiles(false);
    }
  };

  // 删除文件
  const deleteFile = async (filePath: string, isDirectory: boolean) => {
    if (!confirm(`确定要删除 ${filePath} 吗？`)) return;

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/file/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          file_path: filePath,
          force: isDirectory
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert("删除成功!");
      loadFiles(currentPath);
    } catch (error) {
      console.error("删除文件失败:", error);
      alert("删除失败: " + error);
    }
  };

  // 修改权限
  const changePermissions = async (filePath: string, isDirectory: boolean) => {
    const permissions = prompt("请输入权限（如777）:", "777");
    if (!permissions) return;

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/file/chmod", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          file_path: filePath,
          permissions: permissions,
          recursive: isDirectory
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert("权限修改成功!");
      loadFiles(currentPath);
    } catch (error) {
      console.error("修改权限失败:", error);
      alert("权限修改失败: " + error);
    }
  };

  // 文件夹导航
  const navigateToPath = (path: string) => {
    loadFiles(path);
  };

  // 下载文件
  const downloadFile = async (filePath: string, fileName: string) => {
    try {
      // 设置下载开始状态
      setDownloadProgress({
        fileName,
        progress: 0,
        isDownloading: true
      });

      // 使用XMLHttpRequest获取下载进度
      const xhr = new XMLHttpRequest();

      return new Promise<void>((resolve, reject) => {
        xhr.open('POST', 'http://127.0.0.1:8000/api/press/file/download');
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.responseType = 'blob';

        // 监听下载进度
        xhr.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setDownloadProgress(prev => prev ? {
              ...prev,
              progress
            } : null);
          }
        });

        // 监听完成事件
        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            // 创建下载链接
            const blob = xhr.response;
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 完成下载
            setDownloadProgress(prev => prev ? {
              ...prev,
              progress: 100
            } : null);

            // 延迟隐藏进度条
            setTimeout(() => {
              setDownloadProgress(null);
              alert("下载成功!");
            }, 500);

            resolve();
          } else {
            reject(new Error(`HTTP ${xhr.status}`));
          }
        });

        // 监听错误事件
        xhr.addEventListener('error', () => {
          reject(new Error('下载请求失败'));
        });

        // 发送请求
        xhr.send(JSON.stringify({
          session_id: sessionInfo.sessionId,
          file_path: filePath
        }));
      });

    } catch (error) {
      console.error("下载文件失败:", error);
      setDownloadProgress(null);
      alert("下载失败: " + error);
    }
  };

  // 新建文件夹
  const createFolder = async () => {
    const folderName = prompt("请输入文件夹名称:");
    if (!folderName) return;

    const folderPath = `${currentPath}/${folderName}`;

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/file/create-folder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          folder_path: folderPath
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert("文件夹创建成功!");
      loadFiles(currentPath);
    } catch (error) {
      console.error("创建文件夹失败:", error);
      alert("创建文件夹失败: " + error);
    }
  };

  // 上传文件
  const uploadFiles = async (fileList: FileList) => {
    if (fileList.length === 0) return;

    const uploadPromises = Array.from(fileList).map(async (file) => {
      const fileName = file.name;
      setUploadingFiles(prev => [...prev, fileName]);

      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('session_id', sessionInfo.sessionId);
        formData.append('upload_path', currentPath);

        const response = await fetch("http://127.0.0.1:8000/api/press/file/upload", {
          method: "POST",
          body: formData
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          throw new Error(data.message || `HTTP ${response.status}`);
        }

        console.log(`文件 ${fileName} 上传成功`);
        return { success: true, fileName };
      } catch (error) {
        console.error(`文件 ${fileName} 上传失败:`, error);
        return { success: false, fileName, error: String(error) };
      } finally {
        setUploadingFiles(prev => prev.filter(name => name !== fileName));
      }
    });

    const results = await Promise.all(uploadPromises);

    // 显示上传结果
    const successCount = results.filter(r => r.success).length;
    const failedResults = results.filter(r => !r.success);

    if (failedResults.length === 0) {
      alert(`成功上传 ${successCount} 个文件!`);
    } else {
      const failedNames = failedResults.map(r => r.fileName).join(', ');
      alert(`上传完成: 成功 ${successCount} 个，失败 ${failedResults.length} 个\n失败文件: ${failedNames}`);
    }

    // 重新加载文件列表
    loadFiles(currentPath);
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      uploadFiles(files);
    }
    // 清空input以允许重复选择同一文件
    event.target.value = '';
  };

  // 页面加载时加载文件
  useEffect(() => {
    if (sessionInfo) {
      loadFiles("/root");
    }
  }, [sessionInfo]);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">文件管理</h2>
        <div className="flex gap-3">
          <label className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors cursor-pointer">
            📤 上传文件
            <input
              type="file"
              multiple
              onChange={handleFileSelect}
              className="hidden"
            />
          </label>
          <button
            onClick={createFolder}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            📁 新建文件夹
          </button>
          <button
            onClick={() => loadFiles(currentPath)}
            disabled={isLoadingFiles}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            🔄 {isLoadingFiles ? "加载中..." : "刷新"}
          </button>
        </div>
      </div>

      {/* 上传进度提示 */}
      {uploadingFiles.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-4">
          <h3 className="font-medium text-blue-800 mb-2">正在上传文件:</h3>
          <div className="space-y-1">
            {uploadingFiles.map((fileName, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-blue-600">
                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                {fileName}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 下载进度提示 */}
      {downloadProgress && (
        <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-4">
          <h3 className="font-medium text-green-800 mb-2">正在下载文件: {downloadProgress.fileName}</h3>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-green-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${downloadProgress.progress}%` }}
            ></div>
          </div>
          <div className="text-sm text-green-600 mt-1">
            {downloadProgress.progress}% 完成
          </div>
        </div>
      )}

      <div className="grid grid-cols-4 gap-6">
        {/* 快捷目录 */}
        <div className="bg-gray-50 rounded-xl p-4">
          <h3 className="font-medium text-gray-700 mb-3">快捷目录</h3>
          <div className="space-y-2">
            <button
              onClick={() => navigateToPath("/root")}
              className="w-full text-left px-3 py-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              🏠 /root
            </button>
            <button
              onClick={() => navigateToPath("/root/PressControl")}
              className="w-full text-left px-3 py-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              ⚙️ PressControl
            </button>
            <button
              onClick={() => navigateToPath("/userdata")}
              className="w-full text-left px-3 py-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              💾 /userdata
            </button>
          </div>
        </div>

        {/* 文件列表 */}
        <div className="col-span-3">
          <div className="bg-gray-50 rounded-xl p-4 mb-4">
            <p className="text-sm text-gray-600">当前路径: {currentPath}</p>
          </div>

          {isLoadingFiles ? (
            <div className="text-center py-8">
              <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-600">加载文件列表中...</p>
            </div>
          ) : (
            <div className="space-y-2">
              {/* 返回上级目录 */}
              {currentPath !== "/" && (
                <div
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg cursor-pointer hover:bg-yellow-100"
                  onDoubleClick={() => {
                    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
                    navigateToPath(parentPath);
                  }}
                >
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => {
                        const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
                        navigateToPath(parentPath);
                      }}
                      className="text-xl hover:text-2xl transition-all"
                    >
                      ↩️
                    </button>
                    <span className="font-medium">返回上级</span>
                  </div>
                </div>
              )}

              {/* 文件列表 */}
              {files.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 hover:bg-blue-50 rounded-lg">
                  <div
                    className="flex items-center gap-3 flex-1 cursor-pointer"
                    onDoubleClick={() => {
                      if (file.type === "directory") {
                        navigateToPath(file.path);
                      }
                    }}
                  >
                    <span className="text-xl">
                      {file.type === "directory" ? "📁" : "📄"}
                    </span>
                    <div>
                      <span className="font-medium">{file.name}</span>
                      <div className="text-xs text-gray-500">
                        {file.permissions} {file.owner}:{file.group}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{file.type === "directory" ? "--" : `${Math.round(file.size / 1024)}KB`}</span>
                    <span>{new Date(file.modified_time).toLocaleDateString()}</span>
                    <div className="flex gap-2">
                      {file.type !== "directory" && (
                        <button
                          onClick={() => downloadFile(file.path, file.name)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          下载
                        </button>
                      )}
                      <button
                        onClick={() => changePermissions(file.path, file.type === "directory")}
                        className="text-orange-600 hover:text-orange-800"
                      >
                        权限
                      </button>
                      <button
                        onClick={() => deleteFile(file.path, file.type === "directory")}
                        className="text-red-600 hover:text-red-800"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {files.length === 0 && !isLoadingFiles && (
                <div className="text-center py-8 text-gray-500">
                  当前目录为空
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
