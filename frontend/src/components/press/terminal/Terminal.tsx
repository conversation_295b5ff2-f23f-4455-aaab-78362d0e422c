import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 终端历史记录条目
interface TerminalHistoryEntry {
  timestamp: string;
  command: string;
  output: string;
  success: boolean;
  execution_time_ms?: number;
}

interface TerminalProps {
  sessionInfo: SessionInfo;
}

export default function Terminal({ sessionInfo }: TerminalProps) {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // 检查当前会话是否已授权
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/auth/status/${sessionInfo.sessionId}/terminal`);
      const data = await response.json();
      setIsAuthorized(data.authorized);
    } catch (error) {
      console.error('检查权限失败:', error);
      setIsAuthorized(false);
    } finally {
      setIsChecking(false);
    }
  };

  const handleAuthSuccess = () => {
    setIsAuthorized(true);
  };

  if (isChecking) {
    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
        <div className="text-center py-8">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">检查控制台访问权限...</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return <TerminalAuthGuard sessionInfo={sessionInfo} onSuccess={handleAuthSuccess} />;
  }

  return <TerminalContent sessionInfo={sessionInfo} />;
}

// 终端权限验证组件
function TerminalAuthGuard({ sessionInfo, onSuccess }: { sessionInfo: SessionInfo; onSuccess: () => void }) {
  const [passcode, setPasscode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');
  const [currentPasscode, setCurrentPasscode] = useState('');
  const [remainingTime, setRemainingTime] = useState(0);

  // 获取当前口令信息（仅开发环境显示）
  const fetchCurrentPasscode = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/auth/passcode/current');
      const data = await response.json();
      if (data.success) {
        setCurrentPasscode(data.data.passcode);
        setRemainingTime(data.data.remaining_seconds);
      }
    } catch (error) {
      console.error('获取口令失败:', error);
    }
  };

  // 验证口令
  const handleVerify = async () => {
    if (passcode.length < 6) {
      setError('请输入有效口令');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const response = await fetch('http://127.0.0.1:8000/api/auth/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          passcode: passcode,
          feature: "terminal"
        })
      });

      if (response.ok) {
        onSuccess();
      } else {
        const data = await response.json();
        setError(data.detail || '口令错误或已过期');
      }
    } catch (error) {
      setError('网络错误，请重试');
    } finally {
      setIsVerifying(false);
    }
  };

  // 倒计时更新
  useEffect(() => {
    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setRemainingTime(prev => {
          if (prev <= 1) {
            fetchCurrentPasscode(); // 重新获取新口令
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [remainingTime]);

  useEffect(() => {
    fetchCurrentPasscode();
    setPasscode('');
    setError('');
  }, []);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="text-center mb-8">
        <div className="text-6xl mb-4">🔐</div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">控制台访问验证</h2>
        <p className="text-gray-600">此功能需要管理员权限，请输入动态口令</p>
      </div>

      {/* 调试信息 - 开发环境显示当前口令 */}
      {import.meta.env.DEV && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
          <div className="text-sm text-yellow-800">
            🔑 当前口令: <code className="font-mono font-bold text-lg">{currentPasscode}</code>
          </div>
          <div className="text-xs text-yellow-600">
            {Math.floor(remainingTime / 60)}:{(remainingTime % 60).toString().padStart(2, '0')} 后更换
          </div>
        </div>
      )}

      <div className="max-w-sm mx-auto space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            请输入6位动态口令
          </label>
          <input
            type="text"
            value={passcode}
            onChange={(e) => setPasscode(e.target.value.slice(0, 20))}
            onKeyPress={(e) => e.key === 'Enter' && handleVerify()}
            className="w-full px-6 py-4 text-center text-xl font-mono tracking-wide rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none"
            placeholder="123456"
            maxLength={20}
          />
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 text-sm text-center">{error}</p>
          </div>
        )}

        <button
          onClick={handleVerify}
          disabled={passcode.length < 6 || isVerifying}
          className="w-full py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-lg transition-colors"
        >
          {isVerifying ? '验证中...' : '验证权限'}
        </button>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            💡 口令每3分钟更换，本次连接期间有效
          </p>
        </div>
      </div>
    </div>
  );
}

// 原有的终端内容组件
function TerminalContent({ sessionInfo }: { sessionInfo: SessionInfo }) {
  const [terminalHistory, setTerminalHistory] = useState<TerminalHistoryEntry[]>([]);
  const [currentCommand, setCurrentCommand] = useState("");
  const [isExecuting, setIsExecuting] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // 执行命令
  const executeCommand = async (command: string) => {
    if (!command.trim() || isExecuting) return;

    setIsExecuting(true);
    setCurrentCommand("");

    // 重置历史索引
    setHistoryIndex(-1);

    const timestamp = new Date().toLocaleTimeString();

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/terminal/execute", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          command: command.trim()
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.message || response.statusText}`);
      }

      const entry: TerminalHistoryEntry = {
        timestamp,
        command: command.trim(),
        output: data.output || "",
        success: data.success,
        execution_time_ms: data.execution_time_ms
      };

      setTerminalHistory(prev => [...prev, entry]);

      // 添加到命令历史（避免重复）
      if (!commandHistory.includes(command.trim())) {
        setCommandHistory(prev => [command.trim(), ...prev.slice(0, 49)]); // 保留最近50条命令
      }

    } catch (error) {
      console.error("执行命令失败:", error);

      const errorEntry: TerminalHistoryEntry = {
        timestamp,
        command: command.trim(),
        output: `错误: ${error}`,
        success: false
      };
      setTerminalHistory(prev => [...prev, errorEntry]);
    } finally {
      setIsExecuting(false);
    }
  };

  // 加载命令历史
  const loadCommandHistory = async () => {
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/terminal/history", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCommandHistory(data.history);
        }
      }
    } catch (error) {
      console.error("加载命令历史失败:", error);
    }
  };

  // 清屏
  const clearTerminal = () => {
    setTerminalHistory([]);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      executeCommand(currentCommand);
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex < commandHistory.length - 1 ? historyIndex + 1 : historyIndex;
        setHistoryIndex(newIndex);
        setCurrentCommand(commandHistory[newIndex] || "");
      }
    } else if (e.key === "ArrowDown") {
      e.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCurrentCommand(commandHistory[newIndex] || "");
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCurrentCommand("");
      }
    }
  };

  // 页面加载时获取命令历史
  useEffect(() => {
    if (sessionInfo) {
      loadCommandHistory();
    }
  }, [sessionInfo]);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
          💻 Web控制台
          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
            已授权
          </span>
        </h2>
        <div className="flex gap-3">
          <button
            onClick={clearTerminal}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            清屏
          </button>
          <button
            onClick={loadCommandHistory}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            刷新历史
          </button>
        </div>
      </div>

      {/* 终端输出区域 */}
      <div className="bg-gray-900 text-green-400 rounded-xl p-4 font-mono text-sm h-96 overflow-y-auto mb-4 terminal-output">
        <div className="space-y-2">
          {/* 欢迎信息 */}
          {terminalHistory.length === 0 && (
            <div className="text-cyan-400">
              <div>ControllerTool Web Console v1.0</div>
              <div>连接到: {sessionInfo.host} ({sessionInfo.username})</div>
              <div>会话ID: {sessionInfo.sessionId}</div>
              <div className="mt-2">输入命令开始使用控制台...</div>
            </div>
          )}

          {/* 历史命令和输出 */}
          {terminalHistory.map((entry, index) => (
            <div key={index} className="space-y-1">
              {/* 命令行 */}
              <div className="text-yellow-400">
                <span className="text-cyan-400">[{entry.timestamp}]</span>{" "}
                <span className="text-purple-400">root@{sessionInfo.host}:~$</span>{" "}
                <span className="text-white">{entry.command}</span>
                {entry.execution_time_ms && (
                  <span className="text-gray-500"> ({entry.execution_time_ms}ms)</span>
                )}
              </div>

              {/* 输出 */}
              {entry.output && (
                <div className={`whitespace-pre-wrap ${
                  entry.success ? "text-green-400" : "text-red-400"
                }`}>
                  {entry.output}
                </div>
              )}
            </div>
          ))}

          {/* 当前命令提示符 */}
          <div className="flex items-center">
            <span className="text-purple-400">root@{sessionInfo.host}:~$</span>
            <span className="ml-2 animate-pulse text-green-400">■</span>
          </div>
        </div>
      </div>

      {/* 命令输入区域 */}
      <div className="flex gap-3">
        <input
          type="text"
          value={currentCommand}
          onChange={(e) => setCurrentCommand(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="输入命令... (↑↓ 浏览历史, Enter 执行)"
          disabled={isExecuting}
          className="flex-1 px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 font-mono"
        />
        <button
          onClick={() => executeCommand(currentCommand)}
          disabled={isExecuting || !currentCommand.trim()}
          className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isExecuting ? "执行中..." : "发送"}
        </button>
      </div>

      {/* 快捷命令 */}
      <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">快捷命令</h3>
        <div className="flex flex-wrap gap-2">
          {[
            "ps aux --sort=-%cpu | head -10",
            "pgrep -f rtPress",
            "netstat -tunlp",
            "ls -la",
            "pwd",
            "df -h",
            "free -m",
            "ps aux --sort=-%mem | head -20",
            "systemctl status",
            "cd /root/PressControl && ls -la",
            "journalctl -n 50 --no-pager",
            "uptime"
          ].map((cmd) => (
            <button
              key={cmd}
              onClick={() => setCurrentCommand(cmd)}
              className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors font-mono"
            >
              {cmd}
            </button>
          ))}
        </div>
      </div>

      {/* 执行提示 */}
      {isExecuting && (
        <div className="mt-4 flex items-center gap-2 text-blue-600">
          <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="text-sm">正在执行命令...</span>
        </div>
      )}
    </div>
  );
}
