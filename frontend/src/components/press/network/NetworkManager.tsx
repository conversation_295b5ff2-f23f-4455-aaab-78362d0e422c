import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 网络接口信息类型
interface NetworkInterface {
  interface_name: string;
  connection_name: string;
  physical_connected: boolean;
  status: "connected" | "disconnected" | "unavailable";
  ip_address?: string;
  subnet_mask?: string;
  gateway?: string;
  dns_servers: string[];
  mac_address?: string;
  rx_bytes: number;
  tx_bytes: number;
  rx_packets: number;
  tx_packets: number;
}

interface NetworkManagerProps {
  sessionInfo: SessionInfo;
}

export default function NetworkManager({ sessionInfo }: NetworkManagerProps) {
  const [networkInterfaces, setNetworkInterfaces] = useState<NetworkInterface[]>([]);
  const [isLoadingNetwork, setIsLoadingNetwork] = useState(false);
  const [showNetworkConfig, setShowNetworkConfig] = useState(false);
  const [selectedInterface, setSelectedInterface] = useState<NetworkInterface | null>(null);

  // Ping测试状态
  const [pingTarget, setPingTarget] = useState("");
  const [pingCount, setPingCount] = useState(4);
  const [isPinging, setIsPinging] = useState(false);
  const [pingResult, setPingResult] = useState<any>(null);

  // 加载网络状态
  const loadNetworkStatus = async () => {
    setIsLoadingNetwork(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/network/status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      setNetworkInterfaces(data.interfaces);
    } catch (error) {
      console.error("加载网络状态失败:", error);
      alert("加载网络状态失败: " + error);
    } finally {
      setIsLoadingNetwork(false);
    }
  };

  // 配置网络接口
  const configureInterface = async (interfaceData: NetworkInterface, config: {
    ip_address: string;
    subnet_mask: string;
    gateway?: string;
    dns_servers?: string[];
    set_as_default?: boolean;
  }) => {
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/network/configure", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          interface_name: interfaceData.interface_name,
          ...config
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      alert("网络配置更新成功!");
      setShowNetworkConfig(false);
      setSelectedInterface(null);
      loadNetworkStatus();
    } catch (error) {
      console.error("配置网络失败:", error);
      alert("配置网络失败: " + error);
    }
  };

  // 重启网络服务
  const restartNetworkService = async () => {
    if (!confirm("确定要重启网络服务吗？这可能会暂时中断网络连接。")) return;

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/network/restart", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert("网络服务重启成功!");
      setTimeout(() => {
        loadNetworkStatus();
      }, 3000);
    } catch (error) {
      console.error("重启网络服务失败:", error);
      alert("重启网络服务失败: " + error);
    }
  };

  // Ping连通性测试
  const performPingTest = async () => {
    if (!pingTarget) {
      alert("请输入目标IP地址");
      return;
    }

    setIsPinging(true);
    setPingResult(null);

    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/network/ping", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          target_ip: pingTarget,
          count: pingCount
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      setPingResult(data);
    } catch (error) {
      console.error("Ping测试失败:", error);
      setPingResult({
        success: false,
        target_ip: pingTarget,
        packets_sent: 0,
        packets_received: 0,
        packet_loss_percent: 100,
        error_message: `测试失败: ${error}`
      });
    } finally {
      setIsPinging(false);
    }
  };

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
  };

  // 页面加载时获取网络状态
  useEffect(() => {
    if (sessionInfo) {
      loadNetworkStatus();
    }
  }, [sessionInfo]);

  return (
    <>
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800">网络管理</h2>
          <div className="flex gap-3">
            <button
              onClick={loadNetworkStatus}
              disabled={isLoadingNetwork}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              🔄 {isLoadingNetwork ? "加载中..." : "刷新状态"}
            </button>
            <button
              onClick={restartNetworkService}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              🔄 重启网络服务
            </button>
          </div>
        </div>

        {isLoadingNetwork ? (
          <div className="text-center py-8">
            <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-600">加载网络状态中...</p>
          </div>
        ) : (
          <>
            {/* 网口状态卡片 */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              {networkInterfaces.map((iface) => {
                const isPhysicalConnected = iface.physical_connected;

                // 根据物理连接状态确定卡片样式
                let cardStyles = "";
                let statusTextColor = "";
                let statusIcon = "";
                let statusMessage = "";

                if (isPhysicalConnected) {
                  cardStyles = "bg-green-50 border-green-200";
                  statusTextColor = "text-green-600";
                  statusIcon = "🟢";
                  statusMessage = " 网线已连接";
                } else {
                  cardStyles = "bg-red-50 border-red-200";
                  statusTextColor = "text-red-600";
                  statusIcon = "🔴";
                  statusMessage = " 网线未连接";
                }

                return (
                  <div key={iface.interface_name} className={`border rounded-xl p-3 ${cardStyles}`}>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium text-gray-800">{iface.interface_name}</h3>
                        <p className="text-sm text-gray-600">{iface.connection_name}</p>
                        <p className={`text-sm font-medium ${statusTextColor}`}>
                          {statusIcon}{statusMessage}
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedInterface(iface);
                          setShowNetworkConfig(true);
                        }}
                        disabled={!isPhysicalConnected}
                        className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        配置
                      </button>
                    </div>

                    <div className="space-y-1.5 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">IP地址:</span>
                        <span className="font-mono">{iface.ip_address || "未配置"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">子网掩码:</span>
                        <span className="font-mono">{iface.subnet_mask || "未配置"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">网关:</span>
                        <span className="font-mono">{iface.gateway || "未配置"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">MAC地址:</span>
                        <span className="font-mono text-xs">{iface.mac_address || "未知"}</span>
                      </div>
                    </div>

                    {/* 流量统计 */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <div className="text-gray-600">接收</div>
                          <div className="font-mono">{formatBytes(iface.rx_bytes)}</div>
                          <div className="text-xs text-gray-500">{iface.rx_packets} packets</div>
                        </div>
                        <div>
                          <div className="text-gray-600">发送</div>
                          <div className="font-mono">{formatBytes(iface.tx_bytes)}</div>
                          <div className="text-xs text-gray-500">{iface.tx_packets} packets</div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Ping连通测试 */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h3 className="font-medium text-gray-700 mb-4">🌐 网络连通性测试</h3>
              <div className="space-y-4">
                <div className="flex gap-4 items-end">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      目标IP地址
                    </label>
                    <input
                      type="text"
                      value={pingTarget}
                      onChange={(e) => setPingTarget(e.target.value)}
                      placeholder="例如: ******* 或 ***********"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试次数
                    </label>
                    <select
                      value={pingCount}
                      onChange={(e) => setPingCount(parseInt(e.target.value))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={4}>4次</option>
                      <option value={8}>8次</option>
                      <option value={16}>16次</option>
                    </select>
                  </div>
                  <button
                    onClick={performPingTest}
                    disabled={!pingTarget || isPinging}
                    className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                  >
                    {isPinging ? "测试中..." : "🔍 开始测试"}
                  </button>
                </div>

                {pingResult && (
                  <div className={`p-4 rounded-lg border ${
                    pingResult.success
                      ? "bg-green-50 border-green-200"
                      : "bg-red-50 border-red-200"
                  }`}>
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`w-3 h-3 rounded-full ${
                        pingResult.success ? "bg-green-500" : "bg-red-500"
                      }`}></span>
                      <span className="font-medium">
                        {pingResult.success ? "连通正常" : "连通失败"}
                      </span>
                      <span className="text-sm text-gray-600">
                        → {pingResult.target_ip}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">发送包数:</span>
                        <span className="ml-2 font-mono">{pingResult.packets_sent}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">接收包数:</span>
                        <span className="ml-2 font-mono">{pingResult.packets_received}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">丢包率:</span>
                        <span className="ml-2 font-mono">{pingResult.packet_loss_percent}%</span>
                      </div>
                      {pingResult.avg_time_ms && (
                        <div>
                          <span className="text-gray-600">平均延时:</span>
                          <span className="ml-2 font-mono">{pingResult.avg_time_ms}ms</span>
                        </div>
                      )}
                    </div>
                    {pingResult.error_message && (
                      <div className="mt-2 text-sm text-red-600">
                        错误信息: {pingResult.error_message}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {/* 网络配置弹窗 */}
      {showNetworkConfig && selectedInterface && (
        <NetworkConfigModal
          interface={selectedInterface}
          onClose={() => {
            setShowNetworkConfig(false);
            setSelectedInterface(null);
          }}
          onSave={configureInterface}
        />
      )}
    </>
  );
}

// 网络配置弹窗组件
interface NetworkConfigModalProps {
  interface: NetworkInterface;
  onClose: () => void;
  onSave: (interfaceData: NetworkInterface, config: {
    ip_address: string;
    subnet_mask: string;
    gateway?: string;
    dns_servers?: string[];
    set_as_default?: boolean;
  }) => void;
}

function NetworkConfigModal({ interface: iface, onClose, onSave }: NetworkConfigModalProps) {
  const [ipAddress, setIpAddress] = useState(iface.ip_address || "");
  const [subnetMask, setSubnetMask] = useState(iface.subnet_mask || "24");
  const [gateway, setGateway] = useState(iface.gateway || "");
  const [dnsServers, setDnsServers] = useState(iface.dns_servers.join(", "));
  const [setAsDefault, setSetAsDefault] = useState(false);

  const handleSave = () => {
    if (!ipAddress) {
      alert("请输入IP地址");
      return;
    }

    const config = {
      ip_address: ipAddress,
      subnet_mask: subnetMask,
      gateway: gateway || undefined,
      dns_servers: dnsServers ? dnsServers.split(",").map(dns => dns.trim()).filter(dns => dns) : undefined,
      set_as_default: setAsDefault
    };

    onSave(iface, config);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white/90 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-800">配置 {iface.interface_name}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              IP地址 *
            </label>
            <input
              type="text"
              value={ipAddress}
              onChange={(e) => setIpAddress(e.target.value)}
              placeholder="**************"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              子网掩码
            </label>
            <select
              value={subnetMask}
              onChange={(e) => setSubnetMask(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="24">24 (*************)</option>
              <option value="16">16 (***********)</option>
              <option value="8">8 (*********)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              网关 (可选)
            </label>
            <input
              type="text"
              value={gateway}
              onChange={(e) => setGateway(e.target.value)}
              placeholder="192.168.40.1"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              DNS服务器 (可选，用逗号分隔)
            </label>
            <input
              type="text"
              value={dnsServers}
              onChange={(e) => setDnsServers(e.target.value)}
              placeholder="*******, 8.8.4.4"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="setAsDefault"
              checked={setAsDefault}
              onChange={(e) => setSetAsDefault(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="setAsDefault" className="text-sm text-gray-700">
              设为默认路由
            </label>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            保存配置
          </button>
        </div>
      </div>
    </div>
  );
}
