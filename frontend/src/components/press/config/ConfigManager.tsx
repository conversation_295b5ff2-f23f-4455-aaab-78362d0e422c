import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 动态配置结构类型
interface FieldSchema {
  name: string;
  description: string;
  address: string;
  type: string;
}

interface ConfigSchema {
  fields: FieldSchema[];
}

interface ConfigManagerProps {
  sessionInfo: SessionInfo;
}

export default function ConfigManager({ sessionInfo }: ConfigManagerProps) {
  const [configSchema, setConfigSchema] = useState<ConfigSchema | null>(null);
  const [currentConfig, setCurrentConfig] = useState<Record<string, number>>({});
  const [editConfig, setEditConfig] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(false);

  // 获取配置结构
  const fetchConfigSchema = async () => {
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/config/sdo-schema");
      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      setConfigSchema({ fields: data.fields });

      // 初始化默认值
      const defaults: Record<string, number> = {};
      data.fields.forEach((field: FieldSchema) => {
        defaults[field.name] = 0.0;
      });
      setCurrentConfig(defaults);
      setEditConfig({ ...defaults });

    } catch (error) {
      console.error("获取配置结构失败:", error);
      alert("获取配置结构失败: " + error);
    }
  };

  // 读取SDO配置
  const readSDOConfig = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/config/read-sdo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      setCurrentConfig(data.config);
      setEditConfig({ ...data.config });
      alert("SDO配置读取成功!");
    } catch (error) {
      console.error("读取SDO配置失败:", error);
      alert("读取SDO配置失败: " + error);
    } finally {
      setIsLoading(false);
    }
  };

  // 写入SDO配置
  const writeSDOConfig = async () => {
    if (!confirm("确定要写入新的SDO配置吗？")) return;

    setIsLoading(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/config/write-sdo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          config: editConfig
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      setCurrentConfig({ ...editConfig });
      alert("SDO配置写入成功!");
    } catch (error) {
      console.error("写入SDO配置失败:", error);
      alert("写入SDO配置失败: " + error);
    } finally {
      setIsLoading(false);
    }
  };

  // 备份数据库
  const backupDatabase = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/config/backup-database", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert(`数据库备份成功: ${data.backup_path}`);
    } catch (error) {
      console.error("备份数据库失败:", error);
      alert("备份数据库失败: " + error);
    } finally {
      setIsLoading(false);
    }
  };

  // 恢复数据库
  const restoreDatabase = async () => {
    const backupPath = prompt("请输入备份文件路径:");
    if (!backupPath) return;

    if (!confirm("确定要恢复数据库吗？此操作将覆盖当前数据库。")) return;

    setIsLoading(true);
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/config/restore-database", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          backup_path: backupPath
        })
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      alert("数据库恢复成功!");
      // 重新读取配置
      readSDOConfig();
    } catch (error) {
      console.error("恢复数据库失败:", error);
      alert("恢复数据库失败: " + error);
    } finally {
      setIsLoading(false);
    }
  };

  // 重置配置为当前值
  const resetToDefaults = () => {
    if (confirm("确定要重置为当前配置吗？")) {
      setEditConfig({ ...currentConfig });
    }
  };

  // 页面加载时获取配置结构和读取配置
  useEffect(() => {
    if (sessionInfo) {
      fetchConfigSchema().then(() => {
        readSDOConfig();
      });
    }
  }, [sessionInfo]);

  // 动态渲染当前配置显示
  const renderCurrentConfig = () => {
    if (!configSchema) return null;

    return configSchema.fields.map(field => {
      if (!(field.name in currentConfig)) return null;

      return (
        <div key={field.name} className="flex justify-between py-1">
          <span className="text-gray-600">{field.description}:</span>
          <span className="font-medium">{currentConfig[field.name]}</span>
        </div>
      );
    });
  };

  // 动态渲染编辑表单
  const renderEditForm = () => {
    if (!configSchema) return null;

    return (
      <div className="grid grid-cols-2 gap-3">
        {configSchema.fields.map(field => {
          if (!(field.name in editConfig)) return null;

          return (
            <div key={field.name}>
              <label className="block text-sm text-gray-600 mb-1">
                {field.description}
              </label>
              <input
                type="number"
                step={field.name === 'lead_ratio' ? '0.000001' : '0.1'}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={editConfig[field.name] || 0}
                onChange={(e) => setEditConfig(prev => ({
                  ...prev,
                  [field.name]: parseFloat(e.target.value) || 0
                }))}
              />
            </div>
          );
        })}
      </div>
    );
  };

  if (!configSchema) {
    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载配置结构...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">压机配置</h2>
        <div className="flex gap-3">
          <button
            onClick={readSDOConfig}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            📖 {isLoading ? "读取中..." : "读取SDO"}
          </button>
          <button
            onClick={backupDatabase}
            disabled={isLoading}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
          >
            💾 备份数据库
          </button>
          <button
            onClick={restoreDatabase}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50"
          >
            🔄 恢复数据库
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* 当前配置显示 */}
        <div className="bg-gray-50 rounded-xl p-6">
          <h3 className="font-medium text-gray-700 mb-4">当前SDO配置</h3>
          <div className="space-y-1">
            {renderCurrentConfig()}
          </div>
        </div>

        {/* 参数设置 */}
        <div className="bg-gray-50 rounded-xl p-6 flex flex-col min-h-[600px]">
          <h3 className="font-medium text-gray-700 mb-4">参数设置</h3>
          <div className="flex-1 overflow-y-auto">
            {renderEditForm()}
          </div>
          
          <div className="flex gap-3 mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={resetToDefaults}
              className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              重置
            </button>
            <button
              onClick={writeSDOConfig}
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              {isLoading ? "写入中..." : "写入SDO"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
