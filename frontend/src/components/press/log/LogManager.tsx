import { useState, useEffect } from "react";

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

// 日志条目接口
interface LogEntry {
  timestamp: string;
  level: "INFO" | "WARN" | "ERROR" | "DEBUG";
  source: string;
  message: string;
  raw_line: string;
}

// 启动信息类型
interface BootEntry {
  boot_offset: string;
  boot_id: string;
  start_time: string;
  end_time: string;
  display_name: string;
}

interface LogManagerProps {
  sessionInfo: SessionInfo;
}

export default function LogManager({ sessionInfo }: LogManagerProps) {
  const [selectedLogType, setSelectedLogType] = useState<"system" | "ssh" | "network">("system");
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [isLoadingLogs, setIsLoadingLogs] = useState(false);
  const [keywordFilter, setKeywordFilter] = useState("");
  const [levelFilter, setLevelFilter] = useState<"INFO" | "WARN" | "ERROR" | "DEBUG" | "">("");

  // 系统日志相关状态
  const [bootEntries, setBootEntries] = useState<BootEntry[]>([]);
  const [selectedBootId, setSelectedBootId] = useState<string>("0"); // 默认选择当前启动
  const [isLoadingBoots, setIsLoadingBoots] = useState(false);

  // 其他日志类型的行数选择
  const [lines, setLines] = useState(50);

  // 加载启动列表
  const loadBootList = async () => {
    if (selectedLogType !== "system") return;

    setIsLoadingBoots(true);
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/press/log/boot-list/${sessionInfo.sessionId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setBootEntries(data.boot_entries);
        // 如果还没有选择启动项，默认选择最新的（索引0对应当前启动）
        if (!selectedBootId && data.boot_entries.length > 0) {
          setSelectedBootId("0");
        }
      } else {
        throw new Error(data.message || "获取启动列表失败");
      }
    } catch (error) {
      console.error("加载启动列表失败:", error);
      alert("加载启动列表失败: " + error);
    } finally {
      setIsLoadingBoots(false);
    }
  };

  // 加载日志
  const loadLogs = async () => {
    setIsLoadingLogs(true);
    try {
      const requestBody: any = {
        session_id: sessionInfo.sessionId,
        log_type: selectedLogType,
        level_filter: levelFilter || null,
        keyword_filter: keywordFilter || null
      };

      // 如果是系统日志，添加boot_id参数，不添加lines参数
      if (selectedLogType === "system") {
        requestBody.boot_id = selectedBootId;
        // 系统日志不限制行数，显示选择启动的全部日志
      } else {
        // 网络日志和SSH日志使用lines参数
        requestBody.lines = lines;
      }

      const response = await fetch("http://127.0.0.1:8000/api/press/log/query", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setLogEntries(data.entries);
      } else {
        throw new Error(data.message || "获取日志失败");
      }
    } catch (error) {
      console.error("加载日志失败:", error);
      alert("加载日志失败: " + error);
    } finally {
      setIsLoadingLogs(false);
    }
  };

  // 导出日志
  const exportLogs = async () => {
    try {
      const response = await fetch("http://127.0.0.1:8000/api/press/log/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          log_type: selectedLogType,
          lines: 1000,
          level_filter: levelFilter || null,
          keyword_filter: keywordFilter || null
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        // 从完整文件路径中提取文件名（支持Windows和Linux路径）
        const fileName = data.file_path.split(/[/\\]/).pop();
        const downloadUrl = `http://127.0.0.1:8000/api/press/log/download/${fileName}`;
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        alert(`成功导出日志: ${data.message}`);
      } else {
        throw new Error(data.message || "导出日志失败");
      }
    } catch (error) {
      console.error("导出日志失败:", error);
      alert("导出日志失败: " + error);
    }
  };

  // 获取日志级别颜色
  const getLevelColor = (level: string) => {
    switch (level) {
      case "ERROR": return "text-red-400";
      case "WARN": return "text-yellow-400";
      case "DEBUG": return "text-gray-400";
      default: return "text-green-400";
    }
  };

  // 获取日志类型的中文名称
  const getLogTypeName = (type: string) => {
    switch (type) {
      case "system": return "系统日志";
      case "ssh": return "SSH日志";
      case "network": return "网络日志";
      default: return type;
    }
  };

  // 页面加载时和类型切换时加载数据
  useEffect(() => {
    if (sessionInfo) {
      if (selectedLogType === "system") {
        // 系统日志需要先加载启动列表
        loadBootList();
      } else {
        // 其他类型直接加载日志
        loadLogs();
      }
    }
  }, [sessionInfo, selectedLogType, levelFilter, lines]); // 添加lines依赖

  // 当选择的启动项改变时，重新加载系统日志
  useEffect(() => {
    if (sessionInfo && selectedLogType === "system" && selectedBootId) {
      loadLogs();
    }
  }, [selectedBootId]);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">日志管理</h2>
        <div className="flex gap-3">
          <button
            onClick={loadLogs}
            disabled={isLoadingLogs}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            🔄 {isLoadingLogs ? "加载中..." : "刷新"}
          </button>
          <button
            onClick={exportLogs}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            📤 导出日志
          </button>
        </div>
      </div>

      {/* 日志筛选控制 */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 rounded-xl p-4">
          <h3 className="font-medium text-gray-700 mb-3">日志类型和筛选</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">日志类型</label>
              <select
                value={selectedLogType}
                onChange={(e) => setSelectedLogType(e.target.value as "system" | "ssh" | "network")}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="system">系统日志</option>
                <option value="ssh">SSH日志</option>
                <option value="network">网络日志</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">日志级别</label>
              <select
                value={levelFilter}
                onChange={(e) => setLevelFilter(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部级别</option>
                <option value="ERROR">错误 (ERROR)</option>
                <option value="WARN">警告 (WARN)</option>
                <option value="INFO">信息 (INFO)</option>
                <option value="DEBUG">调试 (DEBUG)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-xl p-4">
          <h3 className="font-medium text-gray-700 mb-3">搜索和显示</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">关键词搜索</label>
              <input
                type="text"
                value={keywordFilter}
                onChange={(e) => setKeywordFilter(e.target.value)}
                placeholder="输入关键词搜索日志内容..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 根据日志类型显示不同的选择项 */}
            {selectedLogType === "system" ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">启动选择</label>
                <select
                  value={selectedBootId}
                  onChange={(e) => setSelectedBootId(e.target.value)}
                  disabled={isLoadingBoots}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                >
                  {bootEntries.map((boot) => (
                    <option key={boot.boot_offset} value={boot.boot_offset}>
                      {boot.display_name}
                    </option>
                  ))}
                  {bootEntries.length === 0 && !isLoadingBoots && (
                    <option value="0">当前启动</option>
                  )}
                </select>
                {isLoadingBoots && (
                  <p className="text-xs text-gray-500 mt-1">加载启动列表中...</p>
                )}
                <p className="text-xs text-gray-500 mt-1">ℹ️ 显示选择启动的全部系统日志（最新在上）</p>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">显示行数</label>
                <select
                  value={lines}
                  onChange={(e) => setLines(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={20}>最近 20 行</option>
                  <option value={50}>最近 50 行</option>
                  <option value={100}>最近 100 行</option>
                  <option value={200}>最近 200 行</option>
                  <option value={500}>最近 500 行</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  ℹ️ {selectedLogType === "network" ? "网络日志（最新在上）" : "SSH日志（最新在上）"}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 日志内容 */}
      <div className="bg-gray-900 rounded-xl p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-green-400 font-medium">
            {getLogTypeName(selectedLogType)} - 共 {logEntries.length} 条记录
          </h3>
          {keywordFilter && (
            <span className="text-yellow-400 text-sm">
              关键词: "{keywordFilter}"
            </span>
          )}
        </div>

        <div className="h-96 overflow-y-auto font-mono text-sm">
          {isLoadingLogs ? (
            <div className="text-center py-8">
              <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-400">加载日志中...</p>
            </div>
          ) : logEntries.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">暂无日志记录</p>
            </div>
          ) : (
            <div className="space-y-1">
              {logEntries.map((entry, index) => (
                <div key={index} className="hover:bg-gray-800 p-2 rounded">
                  <div className="flex items-start gap-3">
                    <span className="text-cyan-400 text-xs whitespace-nowrap">
                      {entry.timestamp}
                    </span>
                    <span className={`text-xs font-bold uppercase px-1 rounded ${getLevelColor(entry.level)} bg-gray-800 whitespace-nowrap`}>
                      {entry.level}
                    </span>
                    <span className="text-purple-400 text-xs whitespace-nowrap">
                      {entry.source}
                    </span>
                    <span className="text-white text-xs flex-1 break-all">
                      {entry.message}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 日志统计信息 */}
      <div className="mt-4 grid grid-cols-4 gap-4">
        {["ERROR", "WARN", "INFO", "DEBUG"].map(level => {
          const count = logEntries.filter(entry => entry.level === level).length;
          return (
            <div key={level} className="bg-gray-50 rounded-lg p-3 text-center">
              <div className={`text-lg font-bold ${getLevelColor(level)}`}>
                {count}
              </div>
              <div className="text-sm text-gray-600">{level}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
