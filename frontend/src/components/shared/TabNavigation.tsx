export type TabType = "upgrade" | "config" | "network" | "logs" | "files" | "terminal";

interface Tab {
  id: TabType;
  name: string;
  icon: string;
}

interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export default function TabNavigation({ activeTab, onTabChange }: TabNavigationProps) {
  const tabs: Tab[] = [
    { id: "upgrade", name: "系统升级", icon: "🚀" },
    { id: "config", name: "压机配置", icon: "⚙️" },
    { id: "network", name: "网络配置", icon: "🌐" },
    { id: "logs", name: "系统日志", icon: "📋" },
    { id: "files", name: "文件管理", icon: "📁" },
    { id: "terminal", name: "控制台", icon: "💻" }
  ];

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 mb-6">
      <div className="flex border-b border-gray-200">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center gap-2 px-6 py-4 font-medium transition-all ${
              activeTab === tab.id
                ? "text-blue-600 border-b-2 border-blue-500 bg-blue-50/50"
                : "text-gray-600 hover:text-gray-800 hover:bg-gray-50"
            }`}
          >
            <span className="text-lg">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </div>
    </div>
  );
}
