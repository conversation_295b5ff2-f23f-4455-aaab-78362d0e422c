// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

interface HeaderProps {
  sessionInfo: SessionInfo;
  onDisconnect: () => void;
}

export default function Header({ sessionInfo, onDisconnect }: HeaderProps) {
  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="text-2xl">
            {sessionInfo.productLine === "press" ? (
              <img src="/img/press.png" alt="压机" className="w-6 h-auto" />
            ) : (
              "🌀"
            )}
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-800">
              {sessionInfo.productLine === "press" ? "压机设备" : "拧紧设备"}管理
            </h1>
            <p className="text-sm text-gray-600">
              已连接到 {sessionInfo.host} | 用户: {sessionInfo.username} |
              会话: {sessionInfo.sessionId}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            在线
          </div>
          <button
            onClick={onDisconnect}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            断开连接
          </button>
        </div>
      </div>
    </div>
  );
}
