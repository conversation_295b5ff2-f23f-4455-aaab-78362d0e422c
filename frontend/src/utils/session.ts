export interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

export const getSessionInfo = (): SessionInfo | null => {
  try {
    const sessionData = localStorage.getItem("ssh_session");
    if (!sessionData) return null;
    return JSON.parse(sessionData) as SessionInfo;
  } catch (error) {
    console.error("获取会话信息失败:", error);
    return null;
  }
};

export const setSessionInfo = (session: SessionInfo): void => {
  try {
    localStorage.setItem("ssh_session", JSON.stringify(session));
  } catch (error) {
    console.error("保存会话信息失败:", error);
  }
};

export const clearSessionInfo = (): void => {
  try {
    localStorage.removeItem("ssh_session");
  } catch (error) {
    console.error("清除会话信息失败:", error);
  }
};

export const validateSession = async (sessionId: string): Promise<boolean> => {
  try {
    const response = await fetch(`http://127.0.0.1:8000/api/ssh/sessions/${sessionId}`);
    return response.ok;
  } catch (error) {
    console.error("验证会话失败:", error);
    return false;
  }
};
