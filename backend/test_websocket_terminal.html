<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket终端测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .terminal {
            background-color: #000000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .input-area {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 8px;
            background-color: #333;
            border: 1px solid #555;
            color: #fff;
            border-radius: 3px;
        }
        
        .btn {
            padding: 8px 16px;
            background-color: #007acc;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .btn:hover {
            background-color: #005a9e;
        }
        
        .btn:disabled {
            background-color: #555;
            cursor: not-allowed;
        }
        
        .status {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 3px;
        }
        
        .status.connected {
            background-color: #2d5a2d;
            color: #90ee90;
        }
        
        .status.disconnected {
            background-color: #5a2d2d;
            color: #ff6b6b;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .session-input {
            padding: 6px;
            background-color: #333;
            border: 1px solid #555;
            color: #fff;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket终端测试</h1>
        
        <div class="controls">
            <label>会话ID:</label>
            <input type="text" id="sessionId" class="session-input" placeholder="输入SSH会话ID" value="sess_12345678">
            <button id="connectBtn" class="btn">连接</button>
            <button id="disconnectBtn" class="btn" disabled>断开</button>
        </div>
        
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div id="terminal" class="terminal"></div>
        
        <div class="input-area">
            <input type="text" id="commandInput" class="input-field" placeholder="输入命令..." disabled>
            <button id="sendBtn" class="btn" disabled>发送</button>
            <button id="clearBtn" class="btn">清屏</button>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #888;">
            <p><strong>使用说明:</strong></p>
            <ul>
                <li>首先确保有有效的SSH会话ID</li>
                <li>点击"连接"建立WebSocket连接</li>
                <li>在输入框中输入命令，按Enter或点击发送</li>
                <li>支持交互式命令如: top, htop, tail -f 等</li>
                <li>使用Ctrl+C发送中断信号: 输入 ^C</li>
            </ul>
        </div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;
        
        const sessionIdInput = document.getElementById('sessionId');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const statusDiv = document.getElementById('status');
        const terminal = document.getElementById('terminal');
        const commandInput = document.getElementById('commandInput');
        const sendBtn = document.getElementById('sendBtn');
        const clearBtn = document.getElementById('clearBtn');
        
        // 连接WebSocket
        function connect() {
            const sessionId = sessionIdInput.value.trim();
            if (!sessionId) {
                alert('请输入会话ID');
                return;
            }
            
            const wsUrl = `ws://127.0.0.1:8000/api/press/terminal/ws/${sessionId}`;
            appendToTerminal(`正在连接到: ${wsUrl}\n`, 'info');
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                isConnected = true;
                updateStatus('已连接', true);
                appendToTerminal('WebSocket连接已建立\n', 'success');
                updateButtons();
            };
            
            websocket.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (e) {
                    appendToTerminal(`[解析错误] ${event.data}\n`, 'error');
                }
            };
            
            websocket.onclose = function(event) {
                isConnected = false;
                updateStatus('连接已断开', false);
                appendToTerminal(`连接关闭: ${event.code} - ${event.reason}\n`, 'warning');
                updateButtons();
            };
            
            websocket.onerror = function(error) {
                appendToTerminal(`WebSocket错误: ${error}\n`, 'error');
            };
        }
        
        // 断开连接
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }
        
        // 发送消息
        function sendMessage(type, data) {
            if (websocket && isConnected) {
                const message = { type, ...data };
                websocket.send(JSON.stringify(message));
            }
        }
        
        // 发送命令
        function sendCommand() {
            const command = commandInput.value;
            if (command.trim()) {
                // 处理特殊命令
                if (command === '^C') {
                    sendMessage('input', { data: '\x03' }); // Ctrl+C
                } else {
                    sendMessage('input', { data: command + '\n' });
                }
                commandInput.value = '';
            }
        }
        
        // 处理接收到的消息
        function handleMessage(message) {
            switch (message.type) {
                case 'output':
                    appendToTerminal(message.data);
                    break;
                case 'error':
                    appendToTerminal(`[错误] ${message.message}\n`, 'error');
                    break;
                case 'connected':
                    appendToTerminal(`[系统] ${message.message}\n`, 'success');
                    break;
                default:
                    appendToTerminal(`[未知消息] ${JSON.stringify(message)}\n`, 'warning');
            }
        }
        
        // 添加内容到终端
        function appendToTerminal(text, type = 'normal') {
            const span = document.createElement('span');
            span.textContent = text;
            
            switch (type) {
                case 'error':
                    span.style.color = '#ff6b6b';
                    break;
                case 'success':
                    span.style.color = '#90ee90';
                    break;
                case 'warning':
                    span.style.color = '#ffd93d';
                    break;
                case 'info':
                    span.style.color = '#6bb6ff';
                    break;
            }
            
            terminal.appendChild(span);
            terminal.scrollTop = terminal.scrollHeight;
        }
        
        // 更新状态显示
        function updateStatus(text, connected) {
            statusDiv.textContent = `状态: ${text}`;
            statusDiv.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }
        
        // 更新按钮状态
        function updateButtons() {
            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            commandInput.disabled = !isConnected;
            sendBtn.disabled = !isConnected;
        }
        
        // 清屏
        function clearTerminal() {
            terminal.innerHTML = '';
        }
        
        // 事件监听
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendCommand);
        clearBtn.addEventListener('click', clearTerminal);
        
        commandInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendCommand();
            }
        });
        
        // 初始化
        updateButtons();
        appendToTerminal('WebSocket终端测试工具已就绪\n', 'info');
        appendToTerminal('请输入有效的SSH会话ID并点击连接\n', 'info');
    </script>
</body>
</html>
