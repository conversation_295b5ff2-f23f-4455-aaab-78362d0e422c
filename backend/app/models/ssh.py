from pydantic import BaseModel, validator
from typing import Literal, Optional, List
from datetime import datetime
import re

class SSHConnectionRequest(BaseModel):
    """SSH 连接请求模型"""
    host: str
    port: int = 22
    username: str
    password: str
    timeout: int = 10
    product_line: Literal["press", "tighten"]

    @validator('host')
    def validate_host(cls, v):
        # IP 地址格式验证
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(ip_pattern, v):
            raise ValueError('请输入有效的IP地址')

        # 检查IP地址范围
        parts = v.split('.')
        for part in parts:
            if not 0 <= int(part) <= 255:
                raise ValueError('IP地址格式错误')
        return v

    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v

    @validator('timeout')
    def validate_timeout(cls, v):
        if not 1 <= v <= 60:
            raise ValueError('超时时间必须在1-60秒之间')
        return v

class SSHConnectionResponse(BaseModel):
    """SSH 连接响应模型"""
    session_id: str
    status: Literal["connected", "failed"]
    message: str
    connected_at: Optional[datetime] = None

class SSHSession(BaseModel):
    """SSH 会话信息模型"""
    session_id: str
    host: str
    port: int
    username: str
    product_line: str
    connected_at: datetime
    last_activity: datetime
    status: Literal["connected", "disconnected", "error"]

class SessionRequest(BaseModel):
    """会话请求模型"""
    session_id: str
