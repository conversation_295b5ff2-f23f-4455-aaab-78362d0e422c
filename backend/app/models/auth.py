from pydantic import BaseModel
from typing import Optional
from enum import Enum

class FeatureType(str, Enum):
    TERMINAL = "terminal"
    UPGRADE = "upgrade"
    CONFIG = "config"

class AuthVerifyRequest(BaseModel):
    session_id: str
    passcode: str
    feature: FeatureType

class AuthStatusRequest(BaseModel):
    session_id: str
    feature: FeatureType

class AuthResponse(BaseModel):
    success: bool
    message: str
    authorized_until: Optional[int] = None

class AuthStatusResponse(BaseModel):
    authorized: bool
    feature: FeatureType
    authorized_at: Optional[str] = None
