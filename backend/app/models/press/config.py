# backend/app/models/press/config.py
from pydantic import BaseModel, validator, create_model
from typing import Optional, Dict, Any
import json
import os

def get_sdo_mapping() -> Dict[str, Any]:
    """加载SDO映射配置"""
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "sdo_mapping.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config['sdo_mappings']

def create_dynamic_sdo_config():
    """根据配置文件动态创建SDOConfig类"""
    mapping = get_sdo_mapping()

    # 动态创建字段字典
    fields = {}
    for field_name in mapping.keys():
        fields[field_name] = (float, ...)  # float类型，必填

    # 动态创建Pydantic模型
    DynamicSDOConfig = create_model('SDOConfig', **fields)

    return DynamicSDOConfig

# 动态生成的SDOConfig类
SDOConfig = create_dynamic_sdo_config()

class SDOReadRequest(BaseModel):
    """读取SDO配置请求模型"""
    session_id: str

    @validator('session_id')
    def validate_session_id(cls, v):
        if not v or not v.startswith('sess_'):
            raise ValueError('无效的会话ID')
        return v

class SDOReadResponse(BaseModel):
    """读取SDO配置响应模型"""
    success: bool
    message: str
    config: Optional[SDOConfig] = None

class SDOWriteRequest(BaseModel):
    """写入SDO配置请求模型"""
    session_id: str
    config: SDOConfig

    @validator('session_id')
    def validate_session_id(cls, v):
        if not v or not v.startswith('sess_'):
            raise ValueError('无效的会话ID')
        return v

class SDOWriteResponse(BaseModel):
    """写入SDO配置响应模型"""
    success: bool
    message: str
