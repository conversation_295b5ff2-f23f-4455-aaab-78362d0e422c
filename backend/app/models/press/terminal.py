# backend/app/models/press/terminal.py
from pydantic import BaseModel
from typing import Optional

class CommandExecuteRequest(BaseModel):
    """命令执行请求"""
    session_id: str
    command: str
    working_directory: Optional[str] = None  # 工作目录

class CommandExecuteResponse(BaseModel):
    """命令执行响应"""
    success: bool
    command: str
    output: str
    error_output: Optional[str] = None
    exit_code: Optional[int] = None
    execution_time_ms: Optional[int] = None
    working_directory: Optional[str] = None

class TerminalHistoryRequest(BaseModel):
    """终端历史请求"""
    session_id: str
    limit: int = 50  # 获取最近N条命令历史

class TerminalHistoryResponse(BaseModel):
    """终端历史响应"""
    success: bool
    history: list[str]  # 命令历史列表
