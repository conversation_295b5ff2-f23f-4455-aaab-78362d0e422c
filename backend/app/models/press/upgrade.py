# backend/app/models/press/upgrade.py
from pydantic import BaseModel, validator
from typing import List, Optional, Literal
from enum import Enum

class UpgradeType(str, Enum):
    """升级类型枚举"""
    PRESS_CONTROL = "PressControl"
    DATABASE = "Database"

class UpgradePackageInfo(BaseModel):
    """升级包信息模型"""
    type: UpgradeType
    filename: str
    file_size: int
    upload_time: str
    is_validated: bool
    validation_message: str
    package_path: Optional[str] = None

class UpgradeRequest(BaseModel):
    """升级执行请求模型"""
    session_id: str
    type: UpgradeType

    @validator('session_id')
    def validate_session_id(cls, v):
        if not v or not v.startswith('sess_'):
            raise ValueError('无效的会话ID')
        return v

class UpgradeResponse(BaseModel):
    """升级响应模型"""
    success: bool
    message: str
    upgrade_id: Optional[str] = None

class ValidationRequest(BaseModel):
    """验证请求模型"""
    session_id: str
    type: UpgradeType
    package_path: str

class ValidationResponse(BaseModel):
    """验证响应模型"""
    success: bool
    message: str
    required_files: List[str]
    found_files: List[str]
    missing_files: List[str]

class BackupInfo(BaseModel):
    """备份信息模型"""
    backup_name: str
    type: UpgradeType
    backup_time: str
    backup_size: int
    backup_path: str
    display_name: str

class BackupListResponse(BaseModel):
    """备份列表响应模型"""
    success: bool
    backups: List[BackupInfo]

class RestoreRequest(BaseModel):
    """恢复备份请求模型"""
    session_id: str
    backup_name: str

class RestoreResponse(BaseModel):
    """恢复备份响应模型"""
    success: bool
    message: str

class UpgradeStep(BaseModel):
    """升级步骤模型"""
    step_name: str
    is_completed: bool
    message: str
    timestamp: str

class UpgradeStatus(BaseModel):
    """升级状态模型"""
    is_upgrading: bool
    current_step: str
    progress: int  # 0-100
    steps: List[UpgradeStep]
    log_messages: List[str]
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    success: Optional[bool] = None

class ProcessStatusRequest(BaseModel):
    """进程状态请求模型"""
    session_id: str

class ProcessStatusResponse(BaseModel):
    """进程状态响应模型"""
    success: bool
    is_running: bool
    process_count: int
    message: str
