# backend/app/models/press/network.py
from pydantic import BaseModel, validator
from typing import List, Optional, Literal
from datetime import datetime

class NetworkInterface(BaseModel):
    """网络接口信息模型"""
    interface_name: str  # eth0, eth1, eth2, eth3
    connection_name: str  # eth0-static, eth1-static等
    physical_connected: bool  # 网线是否连接
    status: Literal["connected", "disconnected", "unavailable"]  # nmcli状态
    ip_address: Optional[str] = None  # **************
    subnet_mask: Optional[str] = None  # ************* 或 /24
    gateway: Optional[str] = None  # ************
    dns_servers: List[str] = []  # ["*******", "*******"]
    mac_address: Optional[str] = None  # aa:bb:cc:dd:ee:ff
    rx_bytes: int = 0  # 接收字节数
    tx_bytes: int = 0  # 发送字节数
    rx_packets: int = 0  # 接收包数
    tx_packets: int = 0  # 发送包数

class NetworkStatusRequest(BaseModel):
    """网络状态查询请求"""
    session_id: str

class NetworkStatusResponse(BaseModel):
    """网络状态查询响应"""
    interfaces: List[NetworkInterface]
    total_interfaces: int
    connected_count: int  # 物理连接的网口数量

class NetworkConfigRequest(BaseModel):
    """网络配置修改请求"""
    session_id: str
    interface_name: str  # eth0, eth1, eth2, eth3
    ip_address: str  # **************
    subnet_mask: str = "24"  # 24 或 *************
    gateway: Optional[str] = None  # ************
    dns_servers: List[str] = []  # ["*******"]
    set_as_default: bool = False  # 是否设为默认路由

    @validator('interface_name')
    def validate_interface(cls, v):
        if v not in ['eth0', 'eth1', 'eth2', 'eth3']:
            raise ValueError('接口名必须是eth0-eth3之一')
        return v

    @validator('ip_address')
    def validate_ip(cls, v):
        # 简单IP格式验证
        parts = v.split('.')
        if len(parts) != 4:
            raise ValueError('IP地址格式错误')
        for part in parts:
            if not (0 <= int(part) <= 255):
                raise ValueError('IP地址范围错误')
        return v

class NetworkOperationResponse(BaseModel):
    """网络操作响应模型"""
    success: bool
    message: str
    interface_name: Optional[str] = None

class NetworkPingRequest(BaseModel):
    """网络连通性测试请求"""
    session_id: str
    target_ip: str
    count: int = 4

    @validator('target_ip')
    def validate_target_ip(cls, v):
        parts = v.split('.')
        if len(parts) != 4:
            raise ValueError('目标IP地址格式错误')
        for part in parts:
            if not (0 <= int(part) <= 255):
                raise ValueError('目标IP地址范围错误')
        return v

class NetworkPingResponse(BaseModel):
    """网络连通性测试响应"""
    success: bool
    target_ip: str
    packets_sent: int
    packets_received: int
    packet_loss_percent: float
    avg_time_ms: Optional[float] = None
    output: str
