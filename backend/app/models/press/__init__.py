# backend/app/models/press/__init__.py
"""
压机设备相关数据模型

包含压机设备管理所需的所有Pydantic模型：
- file: 文件管理相关模型
- config: 压机配置相关模型（待实现）
- network: 网络管理相关模型（待实现）
- log: 日志管理相关模型（待实现）
- terminal: 终端操作相关模型（待实现）
"""

from .file import (
    FileInfo,
    DirectoryListRequest,
    DirectoryListResponse,
    FileDeleteRequest,
    FilePermissionRequest,
    FileOperationResponse,
    CreateFolderRequest,
    FileDownloadRequest
)

from .network import (
    NetworkInterface,
    NetworkStatusRequest,
    NetworkStatusResponse,
    NetworkConfigRequest,
    NetworkOperationResponse,
    NetworkPingRequest,
    NetworkPingResponse
)

from .upgrade import (
    UpgradeType,
    UpgradePackageInfo,
    UpgradeRequest,
    UpgradeResponse,
    ValidationRequest,
    ValidationResponse,
    BackupInfo,
    BackupListResponse,
    RestoreRequest,
    RestoreResponse,
    UpgradeStep,
    UpgradeStatus,
    ProcessStatusRequest,
    ProcessStatusResponse
)

__all__ = [
    "FileInfo",
    "DirectoryListRequest",
    "DirectoryListResponse",
    "FileDeleteRequest",
    "FilePermissionRequest",
    "FileOperationResponse",
    "CreateFolderRequest",
    "FileDownloadRequest",
    "NetworkInterface",
    "NetworkStatusRequest",
    "NetworkStatusResponse",
    "NetworkConfigRequest",
    "NetworkOperationResponse",
    "NetworkPingRequest",
    "NetworkPingResponse",
    "UpgradeType",
    "UpgradePackageInfo",
    "UpgradeRequest",
    "UpgradeResponse",
    "ValidationRequest",
    "ValidationResponse",
    "BackupInfo",
    "BackupListResponse",
    "RestoreRequest",
    "RestoreResponse",
    "UpgradeStep",
    "UpgradeStatus",
    "ProcessStatusRequest",
    "ProcessStatusResponse"
]
