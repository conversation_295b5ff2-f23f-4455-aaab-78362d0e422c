# backend/app/models/press/file.py
from pydantic import BaseModel, validator
from typing import List, Optional, Literal
from datetime import datetime

class FileInfo(BaseModel):
    """文件/文件夹信息模型"""
    name: str
    type: Literal["file", "directory"]
    size: int  # 字节数
    permissions: str  # 权限字符串，如 "rwxr-xr-x"
    owner: str
    group: str
    modified_time: str  # 修改为字符串格式，方便前端处理
    path: str  # 完整路径

    class Config:
        # 自动处理JSON序列化
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DirectoryListRequest(BaseModel):
    """目录列表请求模型"""
    session_id: str
    path: str = "/root"

    @validator('path')
    def validate_path(cls, v):
        # 基本路径验证，防止路径注入
        if '..' in v or ';' in v or '|' in v:
            raise ValueError('路径包含非法字符')
        return v

class DirectoryListResponse(BaseModel):
    """目录列表响应模型"""
    current_path: str
    files: List[FileInfo]
    total_count: int

class FileDeleteRequest(BaseModel):
    """文件删除请求模型"""
    session_id: str
    file_path: str
    force: bool = False  # 是否强制删除（目录时使用rm -rf）

    @validator('file_path')
    def validate_file_path(cls, v):
        if '..' in v or ';' in v or '|' in v:
            raise ValueError('文件路径包含非法字符')
        return v

class FilePermissionRequest(BaseModel):
    """文件权限修改请求模型"""
    session_id: str
    file_path: str
    permissions: str = "777"  # 权限模式，默认777
    recursive: bool = False  # 是否递归（目录时使用）

    @validator('file_path')
    def validate_file_path(cls, v):
        if '..' in v or ';' in v or '|' in v:
            raise ValueError('文件路径包含非法字符')
        return v

    @validator('permissions')
    def validate_permissions(cls, v):
        # 验证权限格式（3位八进制数字）
        if not v.isdigit() or len(v) != 3:
            raise ValueError('权限格式错误，应为3位数字如777')
        for digit in v:
            if not '0' <= digit <= '7':
                raise ValueError('权限数字必须在0-7之间')
        return v

class FileOperationResponse(BaseModel):
    """文件操作响应模型"""
    success: bool
    message: str
    file_path: Optional[str] = None

class CreateFolderRequest(BaseModel):
    """新建文件夹请求模型"""
    session_id: str
    folder_path: str

    @validator('folder_path')
    def validate_folder_path(cls, v):
        if '..' in v or ';' in v or '|' in v:
            raise ValueError('文件夹路径包含非法字符')
        return v

class FileDownloadRequest(BaseModel):
    """文件下载请求模型"""
    session_id: str
    file_path: str

    @validator('file_path')
    def validate_file_path(cls, v):
        if '..' in v or ';' in v or '|' in v:
            raise ValueError('文件路径包含非法字符')
        return v
