# backend/app/models/press/log.py
from pydantic import BaseModel, validator
from typing import List, Optional, Literal
from datetime import datetime

class LogEntry(BaseModel):
    """日志条目模型"""
    timestamp: str  # 2025-07-18 10:30:15
    level: Literal["INFO", "WARN", "ERROR", "DEBUG"]  # 日志级别
    source: str  # systemd, ssh, NetworkManager等
    message: str  # 日志内容
    raw_line: str  # 原始日志行

class LogQueryRequest(BaseModel):
    """日志查询请求"""
    session_id: str
    log_type: Literal["system", "ssh", "network"] = "system"  # 日志类型
    lines: int = 50  # 获取行数，默认50行
    level_filter: Optional[Literal["INFO", "WARN", "ERROR", "DEBUG"]] = None  # 级别过滤
    keyword_filter: Optional[str] = None  # 关键词过滤
    since: Optional[str] = None  # 时间范围，如 "1 hour ago"

class LogQueryResponse(BaseModel):
    """日志查询响应"""
    success: bool
    log_type: str
    total_lines: int
    entries: List[LogEntry]
    message: Optional[str] = None

class LogExportRequest(BaseModel):
    """日志导出请求"""
    session_id: str
    log_type: Literal["system", "ssh", "network"] = "system"
    lines: int = 1000  # 导出行数
    level_filter: Optional[Literal["INFO", "WARN", "ERROR", "DEBUG"]] = None
    keyword_filter: Optional[str] = None
    since: Optional[str] = None

    @validator('lines')
    def validate_lines(cls, v):
        if v > 10000:  # 限制导出行数
            raise ValueError('导出行数不能超过10000行')
        return v

class LogExportResponse(BaseModel):
    """日志导出响应"""
    success: bool
    message: str
    file_path: Optional[str] = None  # 导出文件路径
    file_size: Optional[int] = None  # 文件大小
