from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any
from ..models.ssh import SSHConnectionRequest, SSHConnectionResponse
from ..services.ssh_service import session_manager
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ssh", tags=["SSH Management"])

@router.post("/connect", response_model=SSHConnectionResponse)
async def connect_ssh(request: SSHConnectionRequest):
    """
    建立 SSH 连接

    - **host**: 设备IP地址
    - **port**: SSH端口，默认22
    - **username**: 用户名
    - **password**: 密码
    - **timeout**: 连接超时时间（秒）
    - **product_line**: 产品线类型（press/tighten）
    """
    try:
        logger.info(f"收到 SSH 连接请求: {request.host}:{request.port}")
        response = await session_manager.create_connection(request)

        if response.status == "failed":
            logger.error(f"SSH 连接失败: {response.message}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=response.message
            )

        logger.info(f"SSH 连接成功: {response.session_id}")
        return response

    except ValueError as e:
        # 数据验证错误
        logger.error(f"数据验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"数据验证失败: {str(e)}"
        )
    except Exception as e:
        logger.error(f"SSH 连接异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )

@router.get("/sessions/{session_id}")
async def get_session_info(session_id: str) -> Dict[str, Any]:
    """
    获取指定会话信息（用于会话验证）

    - **session_id**: 会话ID
    """
    try:
        session = session_manager.get_session(session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或已过期"
            )

        return {
            "session_id": session_id,
            "status": session["status"],
            "host": session["host"],
            "port": session["port"],
            "username": session["username"],
            "product_line": session["product_line"],
            "connected_at": session["connected_at"],
            "last_activity": session["last_activity"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话信息失败: {str(e)}"
        )

@router.post("/sessions/{session_id}/disconnect")
async def disconnect_session(session_id: str) -> Dict[str, str]:
    """
    断开指定会话连接

    - **session_id**: 会话ID
    """
    try:
        success = await session_manager.disconnect_session(session_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或已断开"
            )

        logger.info(f"SSH 连接已断开: {session_id}")
        return {"message": f"会话 {session_id} 已成功断开"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"断开连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"断开连接失败: {str(e)}"
        )

@router.get("/sessions")
async def get_all_sessions() -> Dict[str, Any]:
    """获取所有活跃会话"""
    try:
        sessions = session_manager.get_all_sessions()
        return {
            "total": len(sessions),
            "sessions": sessions
        }
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话列表失败: {str(e)}"
        )

@router.post("/cleanup")
async def cleanup_inactive_sessions(max_inactive_minutes: int = 30) -> Dict[str, str]:
    """
    清理不活跃的会话

    - **max_inactive_minutes**: 最大不活跃时间（分钟），默认30分钟
    """
    try:
        await session_manager.cleanup_inactive_sessions(max_inactive_minutes)
        return {"message": f"已清理超过 {max_inactive_minutes} 分钟不活跃的会话"}

    except Exception as e:
        logger.error(f"清理会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理会话失败: {str(e)}"
        )

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """健康检查"""
    try:
        sessions = session_manager.get_all_sessions()
        return {
            "status": "healthy",
            "active_sessions": len(sessions),
            "service": "SSH Management"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"健康检查失败: {str(e)}"
        )
