from fastapi import APIRouter, HTTPException
from app.models.auth import *
from app.services.auth_service import auth_service
from app.utils.passcode import get_passcode_info
import time

router = APIRouter(prefix="/api/auth", tags=["authentication"])

@router.post("/verify", response_model=AuthResponse)
async def verify_feature_auth(request: AuthVerifyRequest):
    """验证功能访问权限"""
    success = auth_service.verify_passcode(
        request.session_id,
        request.passcode,
        request.feature
    )

    if success:
        return AuthResponse(
            success=True,
            message=f"{request.feature.value}功能验证成功",
            authorized_until=int(time.time()) + 86400  # 会话有效期内
        )
    else:
        raise HTTPException(
            status_code=401,
            detail="口令错误或已过期"
        )

@router.get("/status/{session_id}/{feature}", response_model=AuthStatusResponse)
async def check_feature_auth(session_id: str, feature: FeatureType):
    """检查功能授权状态"""
    auth_info = auth_service.get_authorization_info(session_id, feature)
    return AuthStatusResponse(
        authorized=auth_info["authorized"],
        feature=feature,
        authorized_at=auth_info.get("authorized_at")
    )

@router.delete("/revoke/{session_id}/{feature}")
async def revoke_feature_auth(session_id: str, feature: FeatureType):
    """撤销功能授权"""
    auth_service.revoke_feature(session_id, feature)
    return {"success": True, "message": f"{feature.value}功能授权已撤销"}

@router.delete("/revoke/{session_id}")
async def revoke_session_auth(session_id: str):
    """撤销会话所有授权"""
    auth_service.revoke_session(session_id)
    return {"success": True, "message": "会话所有授权已撤销"}

@router.get("/passcode/current")
async def get_current_passcode():
    """获取当前口令（调试用）"""
    return {
        "success": True,
        "data": get_passcode_info()
    }
