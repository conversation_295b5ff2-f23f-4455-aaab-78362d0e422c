# backend/app/routers/press/upgrade.py
from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
import os
import tempfile
from typing import List
from ...models.press.upgrade import (
    UpgradeType, UpgradeRequest, UpgradeResponse, ValidationRequest, ValidationResponse,
    BackupListResponse, RestoreRequest, RestoreResponse, UpgradeStatus,
    ProcessStatusRequest, ProcessStatusResponse
)
from ...services.press_service.upgrade_service import UpgradeService
from ...services.press_service.backup_service import BackupService
from ...services.press_service.process_service import ProcessService
from ...services.ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.post("/upload", summary="上传升级包")
async def upload_package(
    session_id: str = Form(...),
    upgrade_type: str = Form(...),  # "PressControl" or "Database"
    file: UploadFile = File(...)
) -> JSONResponse:
    """
    上传升级包文件到远程服务器

    Args:
        session_id: SSH会话ID
        upgrade_type: 升级类型字符串 ("PressControl" 或 "Database")
        file: 上传的ZIP文件
    """
    logger.info(f"开始上传升级包 - 类型: {upgrade_type}, Session: {session_id}")

    try:
        # 验证升级类型
        try:
            upgrade_type_enum = UpgradeType(upgrade_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的升级类型: {upgrade_type}")

        # 验证会话存在
        if not session_manager.get_connection(session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 验证文件类型
        if not file.filename.endswith('.zip'):
            raise HTTPException(status_code=400, detail="只支持ZIP格式的升级包")

        # 确保升级目录存在
        await UpgradeService.ensure_upgrade_directory(session_id)

        # 读取文件内容
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="上传文件为空")

        # 上传文件到远程服务器
        remote_package_path = f"/userdata/upgrade/{upgrade_type_enum.value}_package.zip"

        connection = session_manager.get_connection(session_id)
        async with connection.start_sftp_client() as sftp:
            async with sftp.open(remote_package_path, 'wb') as remote_file:
                await remote_file.write(file_content)

        logger.info(f"升级包上传成功 - 路径: {remote_package_path}, 大小: {len(file_content)} bytes")

        return JSONResponse(content={
            "success": True,
            "message": f"{upgrade_type_enum.value}升级包上传成功",
            "package_path": remote_package_path,
            "file_size": len(file_content)
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传升级包失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.post("/validate", summary="验证升级包")
async def validate_package(request: ValidationRequest) -> ValidationResponse:
    """
    验证升级包内容是否符合要求

    Args:
        request: 验证请求，包含会话ID、升级类型和包路径
    """
    logger.info(f"开始验证升级包 - 类型: {request.type.value}, Session: {request.session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(request.session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 执行验证
        result = await UpgradeService.validate_package(
            request.session_id,
            request.package_path,
            request.type
        )

        return result

    except Exception as e:
        logger.error(f"验证升级包失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")

@router.post("/execute", summary="执行升级")
async def execute_upgrade(request: UpgradeRequest) -> UpgradeResponse:
    """
    执行升级流程

    Args:
        request: 升级请求，包含会话ID和升级类型
    """
    logger.info(f"API调用: 执行升级 - 类型: {request.type.value}, Session: {request.session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(request.session_id):
            logger.warning(f"SSH连接不存在 - Session: {request.session_id}")
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 检查是否已有升级任务在进行
        current_status = await UpgradeService.get_upgrade_status(request.session_id)
        logger.info(f"当前升级状态检查 - is_upgrading: {current_status.is_upgrading}")

        if current_status.is_upgrading:
            logger.warning(f"升级任务已在进行中 - Session: {request.session_id}, 当前步骤: {current_status.current_step}")
            raise HTTPException(
                status_code=400,
                detail=f"升级任务正在进行中: {current_status.current_step}"
            )

        # 执行升级
        logger.info(f"开始执行升级任务 - Session: {request.session_id}, Type: {request.type.value}")
        result = await UpgradeService.execute_upgrade(request.session_id, request.type)
        logger.info(f"升级任务启动结果 - Success: {result.success}, Message: {result.message}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行升级失败: {e}")
        raise HTTPException(status_code=500, detail=f"升级失败: {str(e)}")

@router.get("/status/{session_id}", summary="获取升级状态")
async def get_upgrade_status(session_id: str) -> UpgradeStatus:
    """
    获取当前升级任务的实时状态

    Args:
        session_id: SSH会话ID
    """
    try:
        logger.info(f"API调用: 获取升级状态 - Session: {session_id}")

        # 验证会话存在
        if not session_manager.get_connection(session_id):
            logger.warning(f"SSH连接不存在 - Session: {session_id}")
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 获取升级状态
        status = await UpgradeService.get_upgrade_status(session_id)
        logger.info(f"升级状态查询结果 - Session: {session_id}, is_upgrading: {status.is_upgrading}, current_step: {status.current_step}, progress: {status.progress}")

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取升级状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/process/status/{session_id}", summary="获取进程状态")
async def get_process_status(session_id: str) -> ProcessStatusResponse:
    """
    获取rtPress进程运行状态

    Args:
        session_id: SSH会话ID
    """
    try:
        # 验证会话存在
        if not session_manager.get_connection(session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 获取进程状态
        request = ProcessStatusRequest(session_id=session_id)
        status = await ProcessService.get_process_status(request)

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取进程状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/backup/list/{session_id}", summary="获取备份列表")
async def list_backups(session_id: str) -> BackupListResponse:
    """
    获取所有可用的备份版本列表

    Args:
        session_id: SSH会话ID
    """
    logger.info(f"获取备份列表 - Session: {session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 获取备份列表
        result = await BackupService.list_backups(session_id)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取备份列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")

@router.post("/backup/restore", summary="恢复备份版本")
async def restore_backup(request: RestoreRequest) -> RestoreResponse:
    """
    从备份版本恢复系统

    Args:
        request: 恢复请求，包含会话ID和备份名称
    """
    logger.info(f"开始恢复备份 - 备份: {request.backup_name}, Session: {request.session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(request.session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 检查是否有升级任务在进行
        current_status = await UpgradeService.get_upgrade_status(request.session_id)
        if current_status.is_upgrading:
            raise HTTPException(
                status_code=400,
                detail=f"升级任务正在进行中，无法恢复备份: {current_status.current_step}"
            )

        # 执行恢复
        result = await BackupService.restore_backup(request.session_id, request.backup_name)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复备份失败: {e}")
        raise HTTPException(status_code=500, detail=f"恢复失败: {str(e)}")

@router.post("/process/stop/{session_id}", summary="停止rtPress进程")
async def stop_process(session_id: str) -> JSONResponse:
    """
    手动停止rtPress进程

    Args:
        session_id: SSH会话ID
    """
    logger.info(f"手动停止rtPress进程 - Session: {session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 停止进程
        success, message = await ProcessService.stop_rtpress(session_id)

        return JSONResponse(content={
            "success": success,
            "message": message
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止进程失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止进程失败: {str(e)}")

@router.post("/process/start/{session_id}", summary="启动rtPress进程")
async def start_process(session_id: str) -> JSONResponse:
    """
    手动启动rtPress进程

    Args:
        session_id: SSH会话ID
    """
    logger.info(f"手动启动rtPress进程 - Session: {session_id}")

    try:
        # 验证会话存在
        if not session_manager.get_connection(session_id):
            raise HTTPException(status_code=400, detail="SSH连接不存在")

        # 启动进程
        success, message = await ProcessService.start_rtpress(session_id)

        return JSONResponse(content={
            "success": success,
            "message": message
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动进程失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动进程失败: {str(e)}")
