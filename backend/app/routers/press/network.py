# backend/app/routers/press/network.py
from fastapi import APIRouter, HTTPException
from ...models.press.network import (
    NetworkStatusRequest, NetworkStatusResponse,
    NetworkConfigRequest, NetworkOperationResponse,
    NetworkPingRequest, NetworkPingResponse
)
from ...services.press_service.network_service import NetworkService
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/status", response_model=NetworkStatusResponse)
async def get_network_status(request: NetworkStatusRequest):
    """获取所有网口状态"""
    try:
        logger.info("API调用: 获取网口状态")
        result = await NetworkService.get_interfaces_status(request)
        return result
    except Exception as e:
        logger.error(f"获取网口状态API失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取网口状态失败: {str(e)}")

@router.post("/configure", response_model=NetworkOperationResponse)
async def configure_interface(request: NetworkConfigRequest):
    """配置网口"""
    try:
        logger.info(f"API调用: 配置网口 {request.interface_name}")
        result = await NetworkService.configure_interface(request)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置网口API失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置网口失败: {str(e)}")

@router.post("/ping", response_model=NetworkPingResponse)
async def ping_test(request: NetworkPingRequest):
    """网络连通性测试"""
    try:
        logger.info(f"API调用: Ping测试 {request.target_ip}")
        result = await NetworkService.ping_test(request)
        return result
    except Exception as e:
        logger.error(f"Ping测试API失败: {e}")
        raise HTTPException(status_code=500, detail=f"Ping测试失败: {str(e)}")

@router.post("/restart", response_model=NetworkOperationResponse)
async def restart_network_service(request: NetworkStatusRequest):
    """重启网络服务"""
    try:
        logger.info("API调用: 重启网络服务")
        result = await NetworkService.restart_network_service(request.session_id)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启网络服务API失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启网络服务失败: {str(e)}")
