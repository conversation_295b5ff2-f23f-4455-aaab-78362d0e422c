# backend/app/routers/press/terminal.py
from fastapi import APIRouter, HTTPException
from ...models.press.terminal import (
    CommandExecuteRequest, CommandExecuteResponse,
    TerminalHistoryRequest, TerminalHistoryResponse
)
from ...services.press_service.terminal_service import TerminalService
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/execute", response_model=CommandExecuteResponse)
async def execute_command(request: CommandExecuteRequest):
    """执行终端命令"""
    try:
        logger.info(f"API调用: 执行终端命令 - {request.command}")
        result = await TerminalService.execute_command(request)
        return result
    except Exception as e:
        logger.error(f"执行终端命令API失败: {e}")
        raise HTTPException(status_code=500, detail=f"执行命令失败: {str(e)}")

@router.post("/history", response_model=TerminalHistoryResponse)
async def get_command_history(request: TerminalHistoryRequest):
    """获取命令历史"""
    try:
        logger.info(f"API调用: 获取命令历史 - 会话 {request.session_id}")
        result = await TerminalService.get_command_history(request)
        return result
    except Exception as e:
        logger.error(f"获取命令历史API失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取命令历史失败: {str(e)}")

@router.post("/clear-history")
async def clear_command_history(request: dict):
    """清除命令历史"""
    try:
        session_id = request.get("session_id")
        if not session_id:
            raise HTTPException(status_code=400, detail="缺少session_id参数")

        logger.info(f"API调用: 清除命令历史 - 会话 {session_id}")
        TerminalService.clear_session_history(session_id)

        return {"success": True, "message": "命令历史已清除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除命令历史API失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除命令历史失败: {str(e)}")
