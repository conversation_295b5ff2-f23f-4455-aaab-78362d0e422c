# backend/app/routers/press/file.py
import io
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from ...models.press.file import (
    DirectoryListRequest, DirectoryListResponse,
    FileDeleteRequest, FilePermissionRequest, FileOperationResponse,
    CreateFolderRequest, FileDownloadRequest
)
from ...services.press_service.file_service import FileService
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/list", response_model=DirectoryListResponse)
async def list_directory(request: DirectoryListRequest):
    """获取目录文件列表"""
    try:
        logger.info(f"API调用: 获取目录列表 {request.path}")
        result = await FileService.list_directory(request)
        return result
    except Exception as e:
        logger.error(f"获取目录列表API失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取目录列表失败: {str(e)}")

@router.post("/delete", response_model=FileOperationResponse)
async def delete_file(request: FileDeleteRequest):
    """删除文件或文件夹"""
    try:
        logger.info(f"API调用: 删除文件 {request.file_path}")
        result = await FileService.delete_file(request)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件API失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@router.post("/chmod", response_model=FileOperationResponse)
async def change_permissions(request: FilePermissionRequest):
    """修改文件权限"""
    try:
        logger.info(f"API调用: 修改权限 {request.file_path} -> {request.permissions}")
        result = await FileService.change_permissions(request)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改权限API失败: {e}")
        raise HTTPException(status_code=500, detail=f"修改权限失败: {str(e)}")

@router.post("/create-folder", response_model=FileOperationResponse)
async def create_folder(request: CreateFolderRequest):
    """新建文件夹"""
    try:
        logger.info(f"API调用: 新建文件夹 {request.folder_path}")
        result = await FileService.create_folder(request)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"新建文件夹API失败: {e}")
        raise HTTPException(status_code=500, detail=f"新建文件夹失败: {str(e)}")

@router.post("/download")
async def download_file(request: FileDownloadRequest):
    """下载文件"""
    try:
        logger.info(f"API调用: 下载文件 {request.file_path}")

        # 获取文件内容
        file_content = await FileService.get_file_content(request)

        # 获取文件名
        filename = request.file_path.split('/')[-1]

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type='application/octet-stream',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logger.error(f"下载文件API失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.post("/upload", response_model=FileOperationResponse)
async def upload_file(
    session_id: str = Form(...),
    upload_path: str = Form(...),
    file: UploadFile = File(...)
):
    """上传文件"""
    try:
        logger.info(f"API调用: 上传文件 {file.filename} 到 {upload_path}")

        # 验证文件名
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        # 读取上传的文件内容
        content = await file.read()

        # 调用FileService上传文件
        result = await FileService.upload_file(
            session_id=session_id,
            target_path=upload_path,
            file_content=content,
            filename=file.filename
        )

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文件API失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")
