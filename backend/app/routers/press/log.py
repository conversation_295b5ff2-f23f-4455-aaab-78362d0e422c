# backend/app/routers/press/log.py
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
import os
import tempfile
from ...models.press.log import (
    LogQueryRequest, LogQueryResponse,
    LogExportRequest, LogExportResponse
)
from ...services.press_service.log_service import LogService
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.post("/query", response_model=LogQueryResponse)
async def query_logs(request: LogQueryRequest):
    """查询日志"""
    try:
        logger.info(f"API调用: 查询日志 {request.log_type}")
        result = await LogService.query_logs(request)
        return result
    except Exception as e:
        logger.error(f"查询日志API失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询日志失败: {str(e)}")

@router.post("/export", response_model=LogExportResponse)
async def export_logs(request: LogExportRequest):
    """导出日志"""
    try:
        logger.info(f"API调用: 导出日志 {request.log_type}")
        result = await LogService.export_logs(request)

        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出日志API失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出日志失败: {str(e)}")

@router.get("/download/{file_name}")
async def download_log_file(file_name: str):
    """下载日志文件"""
    try:
        # 使用跨平台的临时目录
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, file_name)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        logger.info(f"下载日志文件: {file_name}")

        return FileResponse(
            path=file_path,
            filename=file_name,
            media_type='text/plain'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载日志文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.get("/boot-list/{session_id}")
async def get_boot_list(session_id: str):
    """获取系统启动列表"""
    try:
        logger.info(f"API调用: 获取启动列表 session_id={session_id}")
        result = await LogService.get_boot_list(session_id)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取启动列表API失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取启动列表失败: {str(e)}")
