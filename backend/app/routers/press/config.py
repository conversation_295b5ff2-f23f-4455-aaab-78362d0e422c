# backend/app/routers/press/config.py
from fastapi import APIRouter, HTTPException
from ...models.press.config import *
from ...models.ssh import SessionRequest
from ...services.press_service.config_service import ConfigService
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/sdo-schema")
async def get_sdo_schema():
    """
    获取SDO配置结构（前端动态生成界面用）

    Returns:
        dict: SDO配置结构信息
    """
    try:
        from ...models.press.config import get_sdo_mapping
        mapping = get_sdo_mapping()

        # 构建前端需要的结构信息，按配置文件顺序
        fields = []

        for field_name, field_config in mapping.items():
            fields.append({
                "name": field_name,
                "description": field_config['description'],
                "address": field_config['address'],
                "type": "number"
            })

        return {
            "success": True,
            "fields": fields
        }

    except Exception as e:
        logger.error(f"获取SDO配置结构失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置结构失败: {str(e)}")

@router.post("/read-sdo", response_model=SDOReadResponse)
async def read_sdo(request: SDOReadRequest) -> SDOReadResponse:
    """
    读取SDO配置

    Args:
        request: 读取请求

    Returns:
        SDOReadResponse: 读取结果

    Raises:
        HTTPException: 参数验证失败时
    """
    try:
        logger.info(f"API调用: 读取SDO配置 - Session: {request.session_id}")

        # 调用业务服务
        response = await ConfigService.read_sdo_config(request.session_id)

        # 记录调用结果
        if response.success:
            logger.info(f"读取SDO配置成功 - Session: {request.session_id}")
        else:
            logger.warning(f"读取SDO配置失败 - Session: {request.session_id}, 错误: {response.message}")

        return response

    except Exception as e:
        error_msg = f"读取SDO配置接口异常: {str(e)}"
        logger.error(f"{error_msg} - Session: {request.session_id}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/write-sdo", response_model=SDOWriteResponse)
async def write_sdo(request: SDOWriteRequest) -> SDOWriteResponse:
    """
    写入SDO配置

    Args:
        request: 写入请求

    Returns:
        SDOWriteResponse: 写入结果

    Raises:
        HTTPException: 参数验证失败时
    """
    try:
        logger.info(f"API调用: 写入SDO配置 - Session: {request.session_id}")

        # 记录配置参数（用于调试）
        config = request.config
        logger.debug(f"写入配置参数 - stroke: [{config.stroke_min}, {config.stroke_max}], "
                    f"velocity: [{config.velocity_min}, {config.velocity_max}], "
                    f"acceleration: [{config.acceleration_min}, {config.acceleration_max}], "
                    f"force: [{config.force_min}, {config.force_max}], "
                    f"lead_ratio: {config.lead_ratio}")

        # 调用业务服务
        response = await ConfigService.write_sdo_config(request)

        # 记录调用结果
        if response.success:
            logger.info(f"写入SDO配置成功 - Session: {request.session_id}")
        else:
            logger.warning(f"写入SDO配置失败 - Session: {request.session_id}, 错误: {response.message}")

        return response

    except Exception as e:
        error_msg = f"写入SDO配置接口异常: {str(e)}"
        logger.error(f"{error_msg} - Session: {request.session_id}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/backup-database")
async def backup_database(request: SessionRequest):
    """
    备份数据库

    Args:
        request: 会话请求

    Returns:
        dict: 备份结果
    """
    try:
        logger.info(f"API调用: 备份数据库 - Session: {request.session_id}")

        # 调用业务服务
        result = await ConfigService.backup_database(request.session_id)

        if result['success']:
            logger.info(f"备份数据库成功 - Session: {request.session_id}")
        else:
            logger.warning(f"备份数据库失败 - Session: {request.session_id}, 错误: {result['message']}")

        return result

    except Exception as e:
        error_msg = f"备份数据库接口异常: {str(e)}"
        logger.error(f"{error_msg} - Session: {request.session_id}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/restore-database")
async def restore_database(request: dict):
    """
    恢复数据库

    Args:
        request: 恢复请求 (包含session_id和backup_path)

    Returns:
        dict: 恢复结果
    """
    try:
        session_id = request.get('session_id')
        backup_path = request.get('backup_path')

        if not session_id or not backup_path:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        logger.info(f"API调用: 恢复数据库 - Session: {session_id}, 备份路径: {backup_path}")

        # 调用业务服务
        result = await ConfigService.restore_database(session_id, backup_path)

        if result['success']:
            logger.info(f"恢复数据库成功 - Session: {session_id}")
        else:
            logger.warning(f"恢复数据库失败 - Session: {session_id}, 错误: {result['message']}")

        return result

    except Exception as e:
        error_msg = f"恢复数据库接口异常: {str(e)}"
        logger.error(f"{error_msg} - Session: {session_id if 'session_id' in locals() else 'unknown'}")
        raise HTTPException(status_code=500, detail=error_msg)
