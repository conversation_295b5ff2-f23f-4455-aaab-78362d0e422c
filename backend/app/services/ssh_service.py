import asyncssh
import uuid
from datetime import datetime
from typing import Dict, Optional
from ..models.ssh import SSHConnectionRequest, SSHConnectionResponse
from ..utils.logger import get_logger
from .auth_service import auth_service

# 获取当前模块的logger
logger = get_logger(__name__)

class SSHSessionManager:
    """SSH 会话管理器"""

    def __init__(self):
        self.sessions: Dict[str, dict] = {}
        logger.info("SSH 会话管理器已初始化")

    async def create_connection(self, request: SSHConnectionRequest) -> SSHConnectionResponse:
        """创建 SSH 连接"""
        session_id = f"sess_{uuid.uuid4().hex[:8]}"

        logger.info(f"尝试连接到 {request.host}:{request.port} 用户: {request.username}")

        try:
            # 建立 SSH 连接 - 简化配置，让 AsyncSSH 自动协商
            connection = await asyncssh.connect(
                host=request.host,
                port=request.port,
                username=request.username,
                password=request.password,
                connect_timeout=request.timeout,
                known_hosts=None,  # 忽略主机密钥检查
            )

            logger.info(f"SSH 连接成功: {request.host}")

            # 存储会话
            self.sessions[session_id] = {
                "connection": connection,
                "host": request.host,
                "port": request.port,
                "username": request.username,
                "product_line": request.product_line,
                "connected_at": datetime.now(),
                "last_activity": datetime.now(),
                "status": "connected"
            }

            logger.info(f"会话已创建: {session_id}")

            return SSHConnectionResponse(
                session_id=session_id,
                status="connected",
                message=f"成功连接到 {request.product_line} 设备",
                connected_at=datetime.now()
            )

        except asyncssh.PermissionDenied:
            logger.error(f"认证失败: {request.host}")
            return SSHConnectionResponse(
                session_id="",
                status="failed",
                message="认证失败，请检查用户名和密码"
            )
        except asyncssh.ConnectionLost:
            logger.error(f"连接丢失: {request.host}")
            return SSHConnectionResponse(
                session_id="",
                status="failed",
                message="连接超时，请检查网络和设备状态"
            )
        except asyncssh.Error as e:
            logger.error(f"SSH 错误: {e}")
            return SSHConnectionResponse(
                session_id="",
                status="failed",
                message=f"SSH 连接错误: {str(e)}"
            )
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return SSHConnectionResponse(
                session_id="",
                status="failed",
                message=f"连接失败: {str(e)}"
            )

    def get_session(self, session_id: str) -> Optional[dict]:
        """获取会话信息"""
        session = self.sessions.get(session_id)
        if session:
            # 更新最后活动时间
            session["last_activity"] = datetime.now()
        return session

    def get_connection(self, session_id: str):
        """获取SSH连接对象"""
        session = self.sessions.get(session_id)
        if session and session.get("status") == "connected":
            return session.get("connection")
        return None

    def get_all_sessions(self) -> Dict[str, dict]:
        """获取所有会话信息（不包含连接对象）"""
        result = {}
        for session_id, session in self.sessions.items():
            result[session_id] = {
                "session_id": session_id,
                "host": session["host"],
                "port": session["port"],
                "username": session["username"],
                "product_line": session["product_line"],
                "connected_at": session["connected_at"],
                "last_activity": session["last_activity"],
                "status": session["status"]
            }
        return result

    async def disconnect_session(self, session_id: str) -> bool:
        """断开会话连接"""
        session = self.sessions.get(session_id)
        if not session:
            return False

        try:
            # 清除认证授权
            auth_service.revoke_session(session_id)

            # 关闭 SSH 连接
            if session["connection"]:
                session["connection"].close()
                await session["connection"].wait_closed()

            # 删除会话
            del self.sessions[session_id]
            logger.info(f"会话已断开: {session_id}")
            return True

        except Exception as e:
            logger.error(f"断开会话失败: {e}")
            return False

    async def cleanup_inactive_sessions(self, max_inactive_minutes: int = 30):
        """清理不活跃的会话"""
        now = datetime.now()
        inactive_sessions = []

        for session_id, session in self.sessions.items():
            inactive_time = (now - session["last_activity"]).total_seconds() / 60
            if inactive_time > max_inactive_minutes:
                inactive_sessions.append(session_id)

        for session_id in inactive_sessions:
            await self.disconnect_session(session_id)
            logger.info(f"清理不活跃会话: {session_id}")

    async def execute_command(self, session_id: str, command: str) -> str:
        """执行SSH命令 - 基础命令执行接口"""
        session = self.get_session(session_id)
        if not session or not session.get("connection"):
            raise Exception("会话不存在或已断开")

        try:
            result = await session["connection"].run(command, check=True)
            return result.stdout
        except asyncssh.ProcessError as e:
            logger.error(f"命令执行失败: {command}, 错误: {e}")
            raise Exception(f"命令执行失败: {e.stderr}")
        except Exception as e:
            logger.error(f"SSH命令执行异常: {e}")
            raise Exception(f"命令执行异常: {str(e)}")

# 全局会话管理器实例
session_manager = SSHSessionManager()
