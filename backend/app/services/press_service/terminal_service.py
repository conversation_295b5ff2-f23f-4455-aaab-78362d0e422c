# backend/app/services/press_service/terminal_service.py
import time
from typing import Dict, List
from ...models.press.terminal import (
    CommandExecuteRequest, CommandExecuteResponse,
    TerminalHistoryRequest, TerminalHistoryResponse
)
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class TerminalService:
    """终端操作服务类"""

    # 存储每个会话的命令历史（内存中，简单实现）
    _command_history: Dict[str, List[str]] = {}

    @staticmethod
    async def execute_command(request: CommandExecuteRequest) -> CommandExecuteResponse:
        """执行终端命令"""
        logger.info(f"执行终端命令: {request.command}")

        start_time = int(time.time() * 1000)

        try:
            # 记录命令历史
            if request.session_id not in TerminalService._command_history:
                TerminalService._command_history[request.session_id] = []

            TerminalService._command_history[request.session_id].append(request.command)

            # 限制历史记录数量
            if len(TerminalService._command_history[request.session_id]) > 100:
                TerminalService._command_history[request.session_id] = \
                    TerminalService._command_history[request.session_id][-100:]

            # 构建完整命令（如果指定了工作目录）
            full_command = request.command
            if request.working_directory:
                full_command = f"cd {request.working_directory} && {request.command}"

            # 执行命令
            try:
                output = await session_manager.execute_command(
                    request.session_id,
                    full_command
                )

                end_time = int(time.time() * 1000)
                execution_time = end_time - start_time

                logger.info(f"命令执行成功: {request.command}, 耗时: {execution_time}ms")

                return CommandExecuteResponse(
                    success=True,
                    command=request.command,
                    output=output,
                    exit_code=0,
                    execution_time_ms=execution_time,
                    working_directory=request.working_directory
                )

            except Exception as cmd_error:
                # 命令执行失败
                end_time = int(time.time() * 1000)
                execution_time = end_time - start_time

                error_msg = str(cmd_error)
                logger.warning(f"命令执行失败: {request.command}, 错误: {error_msg}")

                return CommandExecuteResponse(
                    success=False,
                    command=request.command,
                    output="",
                    error_output=error_msg,
                    exit_code=1,
                    execution_time_ms=execution_time,
                    working_directory=request.working_directory
                )

        except Exception as e:
            logger.error(f"执行终端命令异常: {e}")

            end_time = int(time.time() * 1000)
            execution_time = end_time - start_time

            return CommandExecuteResponse(
                success=False,
                command=request.command,
                output="",
                error_output=f"系统错误: {str(e)}",
                exit_code=-1,
                execution_time_ms=execution_time,
                working_directory=request.working_directory
            )

    @staticmethod
    async def get_command_history(request: TerminalHistoryRequest) -> TerminalHistoryResponse:
        """获取命令历史"""
        logger.info(f"获取命令历史: 会话 {request.session_id}")

        try:
            history = TerminalService._command_history.get(request.session_id, [])

            # 获取最近的N条记录
            recent_history = history[-request.limit:] if len(history) > request.limit else history

            logger.info(f"返回 {len(recent_history)} 条命令历史")

            return TerminalHistoryResponse(
                success=True,
                history=recent_history
            )

        except Exception as e:
            logger.error(f"获取命令历史失败: {e}")

            return TerminalHistoryResponse(
                success=False,
                history=[]
            )

    @staticmethod
    def clear_session_history(session_id: str):
        """清除会话的命令历史"""
        if session_id in TerminalService._command_history:
            del TerminalService._command_history[session_id]
            logger.info(f"已清除会话 {session_id} 的命令历史")
