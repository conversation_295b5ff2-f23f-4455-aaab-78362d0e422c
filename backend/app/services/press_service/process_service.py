# backend/app/services/press_service/process_service.py
import asyncio
from typing import <PERSON>ple
from ...models.press.upgrade import ProcessStatusRequest, ProcessStatusResponse
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class ProcessService:
    """进程管理服务类"""

    @staticmethod
    async def stop_rtpress(session_id: str) -> Tuple[bool, str]:
        """停止rtPress进程"""
        logger.info(f"准备停止rtPress进程 - Session: {session_id}")

        try:
            # 强制杀死rtPress进程
            kill_command = "pkill -9 rtPress"
            try:
                kill_output = await session_manager.execute_command(session_id, kill_command)
                logger.info(f"pkill命令执行完成，输出: {kill_output}")
            except Exception as e:
                # pkill没有找到进程时也会返回非0退出码，这是正常的
                logger.info(f"pkill命令执行完成，异常: {e}")

            # 等待进程完全停止
            await asyncio.sleep(2)

            logger.info("rtPress进程已停止")
            return True, "rtPress进程已成功停止"

        except Exception as e:
            logger.error(f"停止rtPress进程失败: {e}")
            return False, f"停止进程失败: {str(e)}"

    @staticmethod
    async def start_rtpress(session_id: str) -> Tuple[bool, str]:
        """启动rtPress进程"""
        logger.info(f"准备启动rtPress进程 - Session: {session_id}")

        try:
            # 检查rtPress文件是否存在且可执行
            check_file_command = "ls -la /root/PressControl/rtPress"
            await session_manager.execute_command(session_id, check_file_command)

            # 启动rtPress进程 - 使用exec替换bash进程避免多余进程
            start_command = "cd /root/PressControl && exec nice -n -20 ./rtPress >/dev/null 2>&1 &"
            start_output = await session_manager.execute_command(session_id, start_command)
            logger.info(f"启动命令执行完成，输出: {start_output}")

            # 等待进程启动
            logger.info("等待rtPress进程启动...")
            await asyncio.sleep(3)

            # 验证进程是否启动成功
            logger.info("开始验证rtPress进程启动状态...")
            is_running, process_count, message = await ProcessService.check_rtpress_status(session_id)
            logger.info(f"进程状态检查结果: is_running={is_running}, count={process_count}, message={message}")

            if is_running:
                logger.info(f"rtPress进程启动成功 - PID数量: {process_count}")
                return True, f"rtPress进程启动成功 - 运行进程数: {process_count}"
            else:
                logger.error(f"rtPress进程启动失败: {message}")
                return False, f"rtPress进程启动失败: {message}"

        except Exception as e:
            logger.error(f"启动rtPress进程失败: {e}")
            return False, f"启动进程失败: {str(e)}"

    @staticmethod
    async def check_rtpress_status(session_id: str) -> Tuple[bool, int, str]:
        """检查rtPress进程运行状态"""
        logger.info(f"开始检查rtPress进程状态 - Session: {session_id}")

        try:
            # 使用pgrep查询rtPress进程PID
            check_command = "pgrep -f rtPress"
            logger.info(f"执行进程检查命令: {check_command}")

            try:
                # 添加超时机制避免卡死
                output = await asyncio.wait_for(
                    session_manager.execute_command(session_id, check_command),
                    timeout=5.0  # 5秒超时
                )
                logger.info(f"pgrep命令执行成功，输出: '{output}'")
            except asyncio.TimeoutError:
                logger.error("pgrep命令执行超时")
                return False, 0, "进程检查超时"
            except Exception as e:
                # pgrep没有找到进程时会返回退出码1，这是正常情况
                logger.info(f"pgrep命令执行异常: {e}")
                return False, 0, "rtPress进程未运行"

            if not output.strip():
                logger.info("pgrep命令输出为空")
                return False, 0, "rtPress进程未运行"

            # 统计进程数量
            pids = [line.strip() for line in output.strip().split('\n') if line.strip()]
            process_count = len(pids)

            logger.info(f"rtPress进程状态检查 - 运行中，进程数: {process_count}")
            logger.info(f"活跃进程PIDs: {pids}")
            return True, process_count, f"rtPress进程正在运行 - 进程数: {process_count}"

        except Exception as e:
            logger.error(f"检查rtPress进程状态失败: {e}")
            return False, 0, f"状态检查失败: {str(e)}"

    @staticmethod
    async def get_process_status(request: ProcessStatusRequest) -> ProcessStatusResponse:
        """获取进程状态响应"""
        logger.info(f"获取进程状态 - Session: {request.session_id}")

        try:
            is_running, process_count, message = await ProcessService.check_rtpress_status(request.session_id)

            return ProcessStatusResponse(
                success=True,
                is_running=is_running,
                process_count=process_count,
                message=message
            )

        except Exception as e:
            logger.error(f"获取进程状态失败: {e}")
            return ProcessStatusResponse(
                success=False,
                is_running=False,
                process_count=0,
                message=f"获取状态失败: {str(e)}"
            )
