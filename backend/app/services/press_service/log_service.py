# backend/app/services/press_service/log_service.py
import re
import os
import tempfile
from datetime import datetime
from typing import List, Dict, Optional
from ...models.press.log import (
    LogEntry, LogQueryRequest, LogQueryResponse,
    LogExportRequest, LogExportResponse
)
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class LogService:
    """日志管理服务类"""

    @staticmethod
    async def query_logs(request: LogQueryRequest) -> LogQueryResponse:
        """查询日志"""
        logger.info(f"查询日志: {request.log_type}, 行数: {request.lines}")

        try:
            # 根据日志类型构建命令
            boot_id = getattr(request, 'boot_id', None)

            # 系统日志不使用 lines 参数，其他类型使用
            if request.log_type == "system":
                command = LogService._build_log_command(
                    request.log_type,
                    lines=0,  # 系统日志不限制行数
                    since=request.since,
                    boot_id=boot_id
                )
            else:
                command = LogService._build_log_command(
                    request.log_type,
                    request.lines,
                    request.since,
                    boot_id
                )

            # 执行命令获取日志
            output = await session_manager.execute_command(request.session_id, command)

            # 解析日志条目
            entries = LogService._parse_log_output(output, request.log_type)

            # 应用过滤器
            if request.level_filter:
                entries = [e for e in entries if e.level == request.level_filter]

            if request.keyword_filter:
                keyword = request.keyword_filter.lower()
                entries = [e for e in entries if keyword in e.message.lower()]

            logger.info(f"成功查询到 {len(entries)} 条日志")

            return LogQueryResponse(
                success=True,
                log_type=request.log_type,
                total_lines=len(entries),
                entries=entries
            )

        except Exception as e:
            logger.error(f"查询日志失败: {e}")
            return LogQueryResponse(
                success=False,
                log_type=request.log_type,
                total_lines=0,
                entries=[],
                message=f"查询日志失败: {str(e)}"
            )

    @staticmethod
    async def get_boot_list(session_id: str) -> dict:
        """获取系统启动列表"""
        logger.info("获取系统启动列表")

        try:
            # 执行 journalctl --list-boots 命令
            command = "journalctl --list-boots --no-pager"
            output = await session_manager.execute_command(session_id, command)

            boot_entries = []
            lines = output.strip().split('\n')

            for line in lines:
                if not line.strip():
                    continue

                # 解析启动条目行
                # 格式: -4 ******************************** Fri 2025-07-18 03:01:51 UTC—Fri 2025-07-18 08:19:42 UTC
                parts = line.strip().split(None, 2)
                if len(parts) >= 3:
                    boot_offset = parts[0]  # -4, -3, -2, -1, 0
                    boot_id = parts[1]      # UUID
                    time_range = parts[2]   # 时间范围

                    # 解析时间范围
                    if '—' in time_range:
                        start_time, end_time = time_range.split('—', 1)
                        start_time = start_time.strip()
                        end_time = end_time.strip()
                    else:
                        start_time = time_range.strip()
                        end_time = "进行中"

                    boot_entries.append({
                        "boot_offset": boot_offset,
                        "boot_id": boot_id,
                        "start_time": start_time,
                        "end_time": end_time,
                        "display_name": f"启动 {boot_offset} ({start_time})"
                    })

            logger.info(f"成功获取到 {len(boot_entries)} 个启动记录")

            return {
                "success": True,
                "boot_entries": boot_entries,
                "message": f"成功获取到 {len(boot_entries)} 个启动记录"
            }

        except Exception as e:
            logger.error(f"获取启动列表失败: {e}")
            return {
                "success": False,
                "boot_entries": [],
                "message": f"获取启动列表失败: {str(e)}"
            }


    @staticmethod
    def _build_log_command(log_type: str, lines: int = 50, since: Optional[str] = None, boot_id: Optional[str] = None) -> str:
        """构建日志查询命令"""
        if log_type == "system":
            # 系统日志：不显示行数，而是选择查看最近某次启动的全部日志，最新的在上面
            if boot_id is not None:
                command = f"journalctl --no-pager -k -b {boot_id} -r"
            else:
                command = f"journalctl --no-pager -k -b 0 -r"
        elif log_type == "network":
            # 网络日志：显示行数，通过journalctl -u NetworkManager -n x来查看，日志显示最上面是最新的
            command = f"journalctl --no-pager -u NetworkManager -n {lines} -r"
        elif log_type == "ssh":
            # SSH日志：显示行数，通过journalctl -u ssh -n x来查看，日志显示最上面是最新的
            command = f"journalctl --no-pager -u ssh -n {lines} -r"
        else:
            # 默认系统日志
            command = f"journalctl --no-pager -k -b 0 -r"

        return command

    @staticmethod
    def _parse_log_output(output: str, log_type: str) -> List[LogEntry]:
        """解析日志输出"""
        entries = []
        lines = output.strip().split('\n')

        for line in lines:
            if not line.strip():
                continue

            entry = LogService._parse_single_log_line(line, log_type)
            if entry:
                entries.append(entry)

        return entries

    @staticmethod
    def _parse_single_log_line(line: str, log_type: str) -> Optional[LogEntry]:
        """解析单条日志行"""
        try:
            # 所有类型的日志现在都使用 journalctl 格式
            # journalctl 格式: Jul 18 10:30:15 hostname service[pid]: message
            # 或者: 2025-07-18T10:30:15.123456+08:00 hostname service[pid]: message
            # 中文格式: 7月 23 02:03:24 hostname service[pid]: message

            # 尝试匹配不同的时间戳格式
            timestamp_patterns = [
                r'^(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})',  # Jul 18 10:30:15
                r'^(\d{1,2}月\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})',  # 7月 23 02:03:24
                r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',  # 2025-07-18T10:30:15
                r'^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})'  # 2025-07-18 10:30:15
            ]

            timestamp = ""
            remaining_line = line

            for pattern in timestamp_patterns:
                match = re.match(pattern, line)
                if match:
                    timestamp = match.group(1)
                    remaining_line = line[len(match.group(0)):].strip()
                    break

            if not timestamp:
                # 如果没有匹配到时间戳，使用原始行作为消息
                return LogEntry(
                    timestamp="",
                    level="INFO",
                    source=log_type,
                    message=line.strip(),
                    raw_line=line
                )

            # 确定日志级别
            level = "INFO"  # 默认级别
            if "[ERROR]" in line or "error:" in line.lower() or "failed" in line.lower():
                level = "ERROR"
            elif "[WARN]" in line or "warning:" in line.lower() or "warn:" in line.lower():
                level = "WARN"
            elif "[DEBUG]" in line or "debug:" in line.lower():
                level = "DEBUG"
            elif "<info>" in line.lower():
                level = "INFO"
            elif "<warn>" in line.lower():
                level = "WARN"
            elif "<error>" in line.lower():
                level = "ERROR"

            # 确定日志源
            source = log_type
            if "systemd" in line:
                source = "systemd"
            elif "ssh" in line.lower() or "sshd" in line.lower():
                source = "ssh"
            elif "NetworkManager" in line:
                source = "NetworkManager"
            elif "kernel" in line:
                source = "kernel"

            # 提取消息内容，保留完整的日志消息
            # remaining_line 已经是去掉时间戳后的内容
            message = remaining_line

            return LogEntry(
                timestamp=timestamp,
                level=level,
                source=source,
                message=message,
                raw_line=line
            )

        except Exception as e:
            logger.warning(f"解析日志行失败: {e}, 行内容: {line[:100]}")
            return None

    @staticmethod
    async def export_logs(request: LogExportRequest) -> LogExportResponse:
        """导出日志"""
        logger.info(f"导出日志: {request.log_type}, 行数: {request.lines}")

        try:
            # 查询日志
            query_request = LogQueryRequest(
                session_id=request.session_id,
                log_type=request.log_type,
                lines=request.lines,
                level_filter=request.level_filter,
                keyword_filter=request.keyword_filter,
                since=request.since
            )

            query_response = await LogService.query_logs(query_request)

            if not query_response.success:
                return LogExportResponse(
                    success=False,
                    message=query_response.message or "查询日志失败"
                )

            # 生成导出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{request.log_type}_log_{timestamp}.txt"
            
            # 使用跨平台的临时目录
            temp_dir = tempfile.gettempdir()
            file_path = os.path.join(temp_dir, filename)
            
            # 确保临时目录存在
            os.makedirs(temp_dir, exist_ok=True)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"# {request.log_type.upper()} 日志导出\n")
                f.write(f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总行数: {len(query_response.entries)}\n")
                if request.level_filter:
                    f.write(f"# 级别过滤: {request.level_filter}\n")
                if request.keyword_filter:
                    f.write(f"# 关键词过滤: {request.keyword_filter}\n")
                f.write("\n" + "="*80 + "\n\n")

                for entry in query_response.entries:
                    f.write(f"[{entry.timestamp}] [{entry.level}] {entry.source}: {entry.message}\n")

            # 获取文件大小
            file_size = os.path.getsize(file_path)

            logger.info(f"成功导出日志到: {file_path}, 大小: {file_size} bytes")

            return LogExportResponse(
                success=True,
                message=f"成功导出 {len(query_response.entries)} 条日志",
                file_path=file_path,
                file_size=file_size
            )

        except Exception as e:
            logger.error(f"导出日志失败: {e}")
            return LogExportResponse(
                success=False,
                message=f"导出日志失败: {str(e)}"
            )
