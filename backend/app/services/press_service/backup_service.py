# backend/app/services/press_service/backup_service.py
import os
import shutil
import zipfile
from datetime import datetime
from typing import List, Tuple
from ...models.press.upgrade import (
    UpgradeType, BackupInfo, BackupListResponse,
    RestoreRequest, RestoreResponse
)
from ..ssh_service import session_manager
from .process_service import ProcessService
from ...utils.logger import get_logger

logger = get_logger(__name__)

class BackupService:
    """备份管理服务类"""

    BACKUP_BASE_DIR = "/userdata/backup"
    PRESS_CONTROL_DIR = "/root/PressControl"
    ROOT_DIR = "/root"

    @staticmethod
    async def ensure_backup_directory(session_id: str):
        """确保备份目录存在"""
        try:
            # 创建备份基础目录
            command = f"mkdir -p {BackupService.BACKUP_BASE_DIR}"
            await session_manager.execute_command(session_id, command)
            logger.info(f"确保备份目录存在: {BackupService.BACKUP_BASE_DIR}")
        except Exception as e:
            logger.error(f"创建备份目录失败: {e}")
            raise

    @staticmethod
    async def create_backup(session_id: str, upgrade_type: UpgradeType) -> Tuple[bool, str, str]:
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{upgrade_type.value}_{timestamp}"
        backup_path = f"{BackupService.BACKUP_BASE_DIR}/{backup_name}"

        logger.info(f"开始创建备份 - 类型: {upgrade_type.value}, 路径: {backup_path}")

        try:
            # 确保备份目录存在
            await BackupService.ensure_backup_directory(session_id)

            # 创建具体备份目录
            create_dir_command = f"mkdir -p {backup_path}"
            await session_manager.execute_command(session_id, create_dir_command)

            if upgrade_type == UpgradeType.PRESS_CONTROL:
                # 备份下位机文件
                return await BackupService._backup_press_control(session_id, backup_path, backup_name)
            elif upgrade_type == UpgradeType.DATABASE:
                # 备份数据库文件
                return await BackupService._backup_database(session_id, backup_path, backup_name)
            else:
                return False, f"不支持的备份类型: {upgrade_type.value}", ""

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False, f"创建备份失败: {str(e)}", ""

    @staticmethod
    async def _backup_press_control(session_id: str, backup_path: str, backup_name: str) -> Tuple[bool, str, str]:
        """备份下位机文件"""
        try:
            # 检查源目录是否存在
            check_command = f"ls -la {BackupService.PRESS_CONTROL_DIR}"
            await session_manager.execute_command(session_id, check_command)

            # 复制整个PressControl目录
            copy_command = f"cp -r {BackupService.PRESS_CONTROL_DIR}/* {backup_path}/"
            await session_manager.execute_command(session_id, copy_command)

            # 获取备份大小
            size_command = f"du -sb {backup_path} | cut -f1"
            size_output = await session_manager.execute_command(session_id, size_command)
            backup_size = int(size_output.strip()) if size_output.strip().isdigit() else 0

            logger.info(f"下位机备份创建成功 - 路径: {backup_path}, 大小: {backup_size} bytes")
            return True, f"下位机备份创建成功: {backup_name}", backup_path

        except Exception as e:
            logger.error(f"备份下位机文件失败: {e}")
            return False, f"备份下位机文件失败: {str(e)}", ""

    @staticmethod
    async def _backup_database(session_id: str, backup_path: str, backup_name: str) -> Tuple[bool, str, str]:
        """备份数据库文件"""
        try:
            # 数据库文件列表
            db_files = ["press.lx", "press.lx-shm", "press.lx-wal"]

            # 检查并备份每个数据库文件
            for db_file in db_files:
                source_path = f"{BackupService.ROOT_DIR}/{db_file}"

                # 检查文件是否存在
                check_command = f"ls -la {source_path}"
                try:
                    await session_manager.execute_command(session_id, check_command)
                except:
                    logger.warning(f"数据库文件不存在: {source_path}")
                    continue

                # 复制数据库文件
                copy_command = f"cp {source_path} {backup_path}/"
                await session_manager.execute_command(session_id, copy_command)
                logger.info(f"已备份数据库文件: {db_file}")

            # 获取备份大小
            size_command = f"du -sb {backup_path} | cut -f1"
            size_output = await session_manager.execute_command(session_id, size_command)
            backup_size = int(size_output.strip()) if size_output.strip().isdigit() else 0

            logger.info(f"数据库备份创建成功 - 路径: {backup_path}, 大小: {backup_size} bytes")
            return True, f"数据库备份创建成功: {backup_name}", backup_path

        except Exception as e:
            logger.error(f"备份数据库文件失败: {e}")
            return False, f"备份数据库文件失败: {str(e)}", ""

    @staticmethod
    async def list_backups(session_id: str) -> BackupListResponse:
        """获取备份列表"""
        logger.info(f"获取备份列表 - Session: {session_id}")

        try:
            await BackupService.ensure_backup_directory(session_id)

            # 列出备份目录
            list_command = f"ls -la {BackupService.BACKUP_BASE_DIR}"
            output = await session_manager.execute_command(session_id, list_command)

            backups = []
            lines = output.strip().split('\n')

            for line in lines:
                if not line.strip() or line.startswith('total') or '. ' in line or '.. ' in line:
                    continue

                parts = line.split()
                if len(parts) < 9:
                    continue

                # 解析目录信息
                permissions = parts[0]
                if not permissions.startswith('d'):  # 只处理目录
                    continue

                folder_name = parts[8]

                # 解析备份类型和时间
                if folder_name.startswith('PressControl_'):
                    backup_type = UpgradeType.PRESS_CONTROL
                    time_part = folder_name.replace('PressControl_', '')
                elif folder_name.startswith('Database_'):
                    backup_type = UpgradeType.DATABASE
                    time_part = folder_name.replace('Database_', '')
                else:
                    continue

                # 格式化时间显示
                try:
                    backup_time = datetime.strptime(time_part, "%Y%m%d_%H%M%S")
                    display_time = backup_time.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    display_time = time_part

                # 获取备份大小
                backup_path = f"{BackupService.BACKUP_BASE_DIR}/{folder_name}"
                size_command = f"du -sb {backup_path} | cut -f1"
                try:
                    size_output = await session_manager.execute_command(session_id, size_command)
                    backup_size = int(size_output.strip()) if size_output.strip().isdigit() else 0
                except:
                    backup_size = 0

                backup_info = BackupInfo(
                    backup_name=folder_name,
                    type=backup_type,
                    backup_time=display_time,
                    backup_size=backup_size,
                    backup_path=backup_path,
                    display_name=f"{backup_type.value} - {display_time}"
                )

                backups.append(backup_info)

            # 按时间倒序排序
            backups.sort(key=lambda x: x.backup_time, reverse=True)

            logger.info(f"获取到 {len(backups)} 个备份")
            return BackupListResponse(success=True, backups=backups)

        except Exception as e:
            logger.error(f"获取备份列表失败: {e}")
            return BackupListResponse(success=False, backups=[])

    @staticmethod
    async def restore_backup(session_id: str, backup_name: str) -> RestoreResponse:
        """恢复备份版本"""
        logger.info(f"开始恢复备份 - 备份名称: {backup_name}")

        try:
            backup_path = f"{BackupService.BACKUP_BASE_DIR}/{backup_name}"

            # 检查备份是否存在
            check_command = f"ls -la {backup_path}"
            await session_manager.execute_command(session_id, check_command)

            # 确定备份类型
            if backup_name.startswith('PressControl_'):
                upgrade_type = UpgradeType.PRESS_CONTROL
            elif backup_name.startswith('Database_'):
                upgrade_type = UpgradeType.DATABASE
            else:
                return RestoreResponse(success=False, message="无法识别的备份类型")

            # 停止rtPress进程
            stop_success, stop_message = await ProcessService.stop_rtpress(session_id)
            if not stop_success:
                return RestoreResponse(success=False, message=f"停止进程失败: {stop_message}")

            # 执行恢复操作
            if upgrade_type == UpgradeType.PRESS_CONTROL:
                restore_success, restore_message = await BackupService._restore_press_control(session_id, backup_path)
            else:
                restore_success, restore_message = await BackupService._restore_database(session_id, backup_path)

            if not restore_success:
                return RestoreResponse(success=False, message=restore_message)

            # 启动rtPress进程
            start_success, start_message = await ProcessService.start_rtpress(session_id)
            if not start_success:
                logger.warning(f"恢复成功但启动进程失败: {start_message}")
                return RestoreResponse(success=True, message=f"恢复成功，但启动进程失败: {start_message}")

            logger.info(f"备份恢复完成: {backup_name}")
            return RestoreResponse(success=True, message=f"成功恢复到版本: {backup_name}")

        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return RestoreResponse(success=False, message=f"恢复备份失败: {str(e)}")

    @staticmethod
    async def _restore_press_control(session_id: str, backup_path: str) -> Tuple[bool, str]:
        """恢复下位机文件"""
        try:
            # 删除当前PressControl目录
            remove_command = f"rm -rf {BackupService.PRESS_CONTROL_DIR}"
            await session_manager.execute_command(session_id, remove_command)

            # 重新创建PressControl目录
            mkdir_command = f"mkdir -p {BackupService.PRESS_CONTROL_DIR}"
            await session_manager.execute_command(session_id, mkdir_command)

            # 复制备份文件
            copy_command = f"cp -r {backup_path}/* {BackupService.PRESS_CONTROL_DIR}/"
            await session_manager.execute_command(session_id, copy_command)

            # 设置权限
            chmod_command = f"chmod -R 777 {BackupService.PRESS_CONTROL_DIR}"
            await session_manager.execute_command(session_id, chmod_command)

            logger.info("下位机文件恢复成功")
            return True, "下位机文件恢复成功"

        except Exception as e:
            logger.error(f"恢复下位机文件失败: {e}")
            return False, f"恢复下位机文件失败: {str(e)}"

    @staticmethod
    async def _restore_database(session_id: str, backup_path: str) -> Tuple[bool, str]:
        """恢复数据库文件"""
        try:
            # 删除当前数据库文件
            db_files = ["press.lx", "press.lx-shm", "press.lx-wal"]
            for db_file in db_files:
                remove_command = f"rm -f {BackupService.ROOT_DIR}/{db_file}"
                await session_manager.execute_command(session_id, remove_command)

            # 复制备份的数据库文件
            copy_command = f"cp {backup_path}/press.lx* {BackupService.ROOT_DIR}/"
            await session_manager.execute_command(session_id, copy_command)

            # 设置权限
            chmod_command = f"chmod 777 {BackupService.ROOT_DIR}/press.lx*"
            await session_manager.execute_command(session_id, chmod_command)

            logger.info("数据库文件恢复成功")
            return True, "数据库文件恢复成功"

        except Exception as e:
            logger.error(f"恢复数据库文件失败: {e}")
            return False, f"恢复数据库文件失败: {str(e)}"
