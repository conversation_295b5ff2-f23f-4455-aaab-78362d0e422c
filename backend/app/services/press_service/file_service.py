# backend/app/services/press_service/file_service.py
import re
from datetime import datetime
from typing import List
from ...models.press.file import (
    FileInfo, DirectoryListRequest, DirectoryListResponse,
    FileDeleteRequest, FilePermissionRequest, FileOperationResponse,
    CreateFolderRequest, FileDownloadRequest
)
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class FileService:
    """文件管理服务类"""

    @staticmethod
    async def list_directory(request: DirectoryListRequest) -> DirectoryListResponse:
        """获取目录文件列表"""
        logger.info(f"获取目录列表: {request.path}")

        # 构建ls命令，使用-la参数获取详细信息
        command = f"ls -la '{request.path}'"

        try:
            # 执行SSH命令
            output = await session_manager.execute_command(request.session_id, command)

            # 解析ls -la输出
            files = FileService._parse_ls_output(output, request.path)

            logger.info(f"成功获取 {len(files)} 个文件/文件夹")

            return DirectoryListResponse(
                current_path=request.path,
                files=files,
                total_count=len(files)
            )

        except Exception as e:
            logger.error(f"获取目录列表失败: {e}")
            raise Exception(f"获取目录列表失败: {str(e)}")

    @staticmethod
    def _parse_ls_output(output: str, base_path: str) -> List[FileInfo]:
        """解析ls -la命令输出"""
        files = []
        lines = output.strip().split('\n')

        # 跳过第一行（总计信息）
        for line in lines[1:]:
            if not line.strip():
                continue

            # 解析ls -la的每一行
            # 格式: drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 18 10:30 filename
            parts = line.split()
            if len(parts) < 9:
                continue

            permissions = parts[0]
            owner = parts[2]
            group = parts[3]
            size = int(parts[4]) if parts[4].isdigit() else 0

            # 文件名可能包含空格，需要特殊处理
            filename = ' '.join(parts[8:])

            # 跳过当前目录和上级目录
            if filename in ['.', '..']:
                continue

            # 判断文件类型
            file_type = "directory" if permissions.startswith('d') else "file"

            # 构建完整路径
            full_path = f"{base_path.rstrip('/')}/{filename}"

            # 解析修改时间（简化处理，格式化为字符串）
            try:
                # parts[5] parts[6] parts[7] 是日期时间信息
                time_str = f"{parts[5]} {parts[6]} {parts[7]}"
                # 格式化为字符串，前端容易处理
                modified_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            except:
                modified_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            file_info = FileInfo(
                name=filename,
                type=file_type,
                size=size,
                permissions=permissions,
                owner=owner,
                group=group,
                modified_time=modified_time,
                path=full_path
            )

            files.append(file_info)

        return files

    @staticmethod
    async def delete_file(request: FileDeleteRequest) -> FileOperationResponse:
        """删除文件或文件夹"""
        logger.info(f"删除文件: {request.file_path}")

        try:
            # 首先检查文件是否存在
            check_command = f"ls -la '{request.file_path}'"
            await session_manager.execute_command(request.session_id, check_command)

            # 构建删除命令
            if request.force:
                # 强制删除，适用于目录
                command = f"rm -rf '{request.file_path}'"
            else:
                # 普通删除
                command = f"rm '{request.file_path}'"

            # 执行删除命令
            await session_manager.execute_command(request.session_id, command)

            logger.info(f"成功删除文件: {request.file_path}")

            return FileOperationResponse(
                success=True,
                message=f"成功删除: {request.file_path}",
                file_path=request.file_path
            )

        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return FileOperationResponse(
                success=False,
                message=f"删除失败: {str(e)}",
                file_path=request.file_path
            )

    @staticmethod
    async def change_permissions(request: FilePermissionRequest) -> FileOperationResponse:
        """修改文件权限"""
        logger.info(f"修改文件权限: {request.file_path} -> {request.permissions}")

        try:
            # 构建chmod命令
            if request.recursive:
                command = f"chmod -R {request.permissions} '{request.file_path}'"
            else:
                command = f"chmod {request.permissions} '{request.file_path}'"

            # 执行权限修改命令
            await session_manager.execute_command(request.session_id, command)

            logger.info(f"成功修改权限: {request.file_path}")

            return FileOperationResponse(
                success=True,
                message=f"成功修改权限为 {request.permissions}: {request.file_path}",
                file_path=request.file_path
            )

        except Exception as e:
            logger.error(f"修改权限失败: {e}")
            return FileOperationResponse(
                success=False,
                message=f"权限修改失败: {str(e)}",
                file_path=request.file_path
            )

    @staticmethod
    async def create_folder(request: CreateFolderRequest) -> FileOperationResponse:
        """新建文件夹"""
        logger.info(f"新建文件夹: {request.folder_path}")

        try:
            # 构建mkdir命令
            command = f"mkdir -p '{request.folder_path}'"

            # 执行创建文件夹命令
            await session_manager.execute_command(request.session_id, command)

            logger.info(f"成功创建文件夹: {request.folder_path}")

            return FileOperationResponse(
                success=True,
                message=f"成功创建文件夹: {request.folder_path}",
                file_path=request.folder_path
            )

        except Exception as e:
            logger.error(f"创建文件夹失败: {e}")
            return FileOperationResponse(
                success=False,
                message=f"创建文件夹失败: {str(e)}",
                file_path=request.folder_path
            )

    @staticmethod
    async def get_file_content(request: FileDownloadRequest) -> bytes:
        """获取文件内容用于下载"""
        logger.info(f"获取文件内容: {request.file_path}")

        try:
            # 获取SSH连接
            connection = session_manager.get_connection(request.session_id)
            if not connection:
                raise Exception("SSH连接不存在")

            # 使用SFTP读取文件内容（支持二进制文件）
            async with connection.start_sftp_client() as sftp:
                async with sftp.open(request.file_path, 'rb') as remote_file:
                    file_content = await remote_file.read()

            logger.info(f"成功获取文件内容: {request.file_path} ({len(file_content)} bytes)")

            return file_content

        except Exception as e:
            logger.error(f"获取文件内容失败: {e}")
            raise Exception(f"获取文件内容失败: {str(e)}")

    @staticmethod
    async def upload_file(session_id: str, target_path: str, file_content: bytes, filename: str) -> FileOperationResponse:
        """上传文件到远程服务器"""
        logger.info(f"上传文件: {filename} 到 {target_path}")

        try:
            # 构建完整的目标文件路径
            full_path = f"{target_path.rstrip('/')}/{filename}"

            # 获取SSH连接
            connection = session_manager.get_connection(session_id)
            if not connection:
                raise Exception("SSH连接不存在")

            # 使用asyncssh的SFTP功能上传文件
            async with connection.start_sftp_client() as sftp:
                # 确保目标目录存在
                try:
                    await sftp.makedirs(target_path.rstrip('/'), exist_ok=True)
                except:
                    pass  # 目录可能已存在

                # 写入文件内容
                async with sftp.open(full_path, 'wb') as remote_file:
                    await remote_file.write(file_content)

            logger.info(f"成功上传文件: {full_path}")

            return FileOperationResponse(
                success=True,
                message=f"成功上传文件: {filename}",
                file_path=full_path
            )

        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return FileOperationResponse(
                success=False,
                message=f"上传文件失败: {str(e)}",
                file_path=f"{target_path}/{filename}"
            )
