# backend/app/services/press_service/network_service.py
import re
from typing import List, Dict, Optional
from ...models.press.network import (
    NetworkInterface, NetworkStatusRequest, NetworkStatusResponse,
    NetworkConfigRequest, NetworkOperationResponse,
    NetworkPingRequest, NetworkPingResponse
)
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class NetworkService:
    """网络管理服务类 - 基于NetworkManager (nmcli)"""

    # 4个网口名称
    INTERFACES = ['eth0', 'eth1', 'eth2', 'eth3']

    @staticmethod
    async def get_interfaces_status(request: NetworkStatusRequest) -> NetworkStatusResponse:
        """获取所有网口状态"""
        logger.info("获取网口状态信息")

        try:
            interfaces = []

            for interface in NetworkService.INTERFACES:
                interface_info = await NetworkService._get_single_interface_status(
                    request.session_id, interface
                )
                interfaces.append(interface_info)

            connected_count = sum(1 for iface in interfaces if iface.physical_connected)

            logger.info(f"成功获取 {len(interfaces)} 个网口状态，{connected_count} 个已连接")

            return NetworkStatusResponse(
                interfaces=interfaces,
                total_interfaces=len(interfaces),
                connected_count=connected_count
            )

        except Exception as e:
            logger.error(f"获取网口状态失败: {e}")
            raise Exception(f"获取网口状态失败: {str(e)}")

    @staticmethod
    async def _get_single_interface_status(session_id: str, interface_name: str) -> NetworkInterface:
        """获取单个网口状态"""
        connection_name = f"{interface_name}-static"

        # 1. 检查物理连接状态
        physical_connected = await NetworkService._check_physical_connection(session_id, interface_name)

        # 2. 获取nmcli设备状态
        device_status = await NetworkService._get_device_status(session_id, interface_name)

        # 3. 获取连接配置信息
        connection_info = await NetworkService._get_connection_info(session_id, connection_name)

        # 4. 获取流量统计
        traffic_stats = await NetworkService._get_traffic_stats(session_id, interface_name)

        return NetworkInterface(
            interface_name=interface_name,
            connection_name=connection_name,
            physical_connected=physical_connected,
            status=device_status.get('status', 'unavailable'),
            ip_address=connection_info.get('ip_address'),
            subnet_mask=connection_info.get('subnet_mask'),
            gateway=connection_info.get('gateway'),
            dns_servers=connection_info.get('dns_servers', []),
            mac_address=device_status.get('mac_address'),
            rx_bytes=traffic_stats.get('rx_bytes', 0),
            tx_bytes=traffic_stats.get('tx_bytes', 0),
            rx_packets=traffic_stats.get('rx_packets', 0),
            tx_packets=traffic_stats.get('tx_packets', 0)
        )

    @staticmethod
    async def _check_physical_connection(session_id: str, interface_name: str) -> bool:
        """检查物理网线连接状态"""
        try:
            command = f"cat /sys/class/net/{interface_name}/carrier"
            output = await session_manager.execute_command(session_id, command)
            return output.strip() == "1"
        except:
            return False

    @staticmethod
    async def _get_device_status(session_id: str, interface_name: str) -> Dict:
        """获取设备状态信息"""
        try:
            # nmcli device show interface_name
            command = f"nmcli device show {interface_name}"
            output = await session_manager.execute_command(session_id, command)

            status_info = {}
            for line in output.split('\n'):
                if 'GENERAL.STATE:' in line:
                    state = line.split(':', 1)[1].strip()
                    # 映射nmcli状态到我们的状态
                    if 'connected' in state.lower():
                        status_info['status'] = 'connected'
                    elif 'disconnected' in state.lower():
                        status_info['status'] = 'disconnected'
                    else:
                        status_info['status'] = 'unavailable'
                elif 'GENERAL.HWADDR:' in line:
                    status_info['mac_address'] = line.split(':', 1)[1].strip()

            return status_info
        except:
            return {'status': 'unavailable'}

    @staticmethod
    async def _get_connection_info(session_id: str, connection_name: str) -> Dict:
        """获取连接配置信息"""
        try:
            command = f"nmcli connection show {connection_name}"
            output = await session_manager.execute_command(session_id, command)

            config_info = {}
            dns_servers = []

            for line in output.split('\n'):
                if 'ipv4.addresses:' in line:
                    addresses = line.split(':', 1)[1].strip()
                    if addresses and addresses != '--':
                        # 格式: **************/24
                        if '/' in addresses:
                            ip, mask = addresses.split('/')
                            config_info['ip_address'] = ip.strip()
                            config_info['subnet_mask'] = mask.strip()
                elif 'ipv4.gateway:' in line:
                    gateway = line.split(':', 1)[1].strip()
                    if gateway and gateway != '--':
                        config_info['gateway'] = gateway
                elif 'ipv4.dns:' in line:
                    dns = line.split(':', 1)[1].strip()
                    if dns and dns != '--':
                        dns_servers.extend(dns.replace(',', ' ').split())

            if dns_servers:
                config_info['dns_servers'] = dns_servers

            return config_info
        except:
            return {}

    @staticmethod
    async def _get_traffic_stats(session_id: str, interface_name: str) -> Dict:
        """获取流量统计信息"""
        try:
            command = f"cat /proc/net/dev | grep {interface_name}:"
            output = await session_manager.execute_command(session_id, command)

            if not output.strip():
                return {}

            # 解析/proc/net/dev格式
            # eth0: bytes packets errs drop fifo frame compressed multicast
            parts = output.split()
            if len(parts) >= 17:
                return {
                    'rx_bytes': int(parts[1]),
                    'rx_packets': int(parts[2]),
                    'tx_bytes': int(parts[9]),
                    'tx_packets': int(parts[10])
                }
            return {}
        except:
            return {}

    @staticmethod
    async def configure_interface(request: NetworkConfigRequest) -> NetworkOperationResponse:
        """配置网口"""
        logger.info(f"配置网口: {request.interface_name} -> {request.ip_address}/{request.subnet_mask}")

        try:
            connection_name = f"{request.interface_name}-static"

            # 1. 修改IP地址
            ip_config = f"{request.ip_address}/{request.subnet_mask}"
            modify_ip_cmd = f"nmcli connection modify {connection_name} ipv4.addresses {ip_config}"
            await session_manager.execute_command(request.session_id, modify_ip_cmd)

            # 2. 设置网关（如果提供）
            if request.gateway:
                gateway_cmd = f"nmcli connection modify {connection_name} ipv4.gateway {request.gateway}"
                await session_manager.execute_command(request.session_id, gateway_cmd)

                # 设置是否为默认路由
                default_cmd = f"nmcli connection modify {connection_name} ipv4.never-default {'no' if request.set_as_default else 'yes'}"
                await session_manager.execute_command(request.session_id, default_cmd)

            # 3. 设置DNS（如果提供）
            if request.dns_servers:
                dns_list = ','.join(request.dns_servers)
                dns_cmd = f"nmcli connection modify {connection_name} ipv4.dns '{dns_list}'"
                await session_manager.execute_command(request.session_id, dns_cmd)

            # 4. 激活连接
            up_cmd = f"nmcli connection up {connection_name}"
            await session_manager.execute_command(request.session_id, up_cmd)

            logger.info(f"成功配置网口: {request.interface_name}")

            return NetworkOperationResponse(
                success=True,
                message=f"成功配置 {request.interface_name}: {request.ip_address}/{request.subnet_mask}",
                interface_name=request.interface_name
            )

        except Exception as e:
            logger.error(f"配置网口失败: {e}")
            return NetworkOperationResponse(
                success=False,
                message=f"配置网口失败: {str(e)}",
                interface_name=request.interface_name
            )

    @staticmethod
    async def ping_test(request: NetworkPingRequest) -> NetworkPingResponse:
        """网络连通性测试"""
        logger.info(f"Ping测试: {request.target_ip}")

        try:
            command = f"ping -c {request.count} {request.target_ip}"
            output = await session_manager.execute_command(request.session_id, command)

            # 解析ping输出
            lines = output.split('\n')
            packets_sent = request.count
            packets_received = 0
            packet_loss_percent = 100.0
            avg_time_ms = None

            for line in lines:
                if 'packets transmitted' in line and 'received' in line:
                    # 格式: 4 packets transmitted, 4 received, 0% packet loss
                    parts = line.split()
                    if len(parts) >= 4:
                        packets_received = int(parts[3])
                        packet_loss_percent = float(parts[5].replace('%', ''))
                elif 'rtt min/avg/max' in line or 'round-trip min/avg/max' in line:
                    # 格式: rtt min/avg/max/mdev = 0.089/0.141/0.191/0.037 ms
                    # 或: round-trip min/avg/max/stddev = 1.234/2.345/3.456/0.123 ms
                    times = line.split('=')[1].strip().split('/')[1]
                    avg_time_ms = float(times)

            success = packet_loss_percent < 100

            return NetworkPingResponse(
                success=success,
                target_ip=request.target_ip,
                packets_sent=packets_sent,
                packets_received=packets_received,
                packet_loss_percent=packet_loss_percent,
                avg_time_ms=avg_time_ms,
                output=output
            )

        except Exception as e:
            logger.error(f"Ping测试失败: {e}")
            return NetworkPingResponse(
                success=False,
                target_ip=request.target_ip,
                packets_sent=request.count,
                packets_received=0,
                packet_loss_percent=100.0,
                output=f"Ping测试失败: {str(e)}"
            )

    @staticmethod
    async def restart_network_service(session_id: str) -> NetworkOperationResponse:
        """重启网络服务"""
        logger.info("重启网络服务")

        try:
            # 重启NetworkManager服务
            command = "systemctl restart NetworkManager"
            await session_manager.execute_command(session_id, command)

            logger.info("成功重启网络服务")

            return NetworkOperationResponse(
                success=True,
                message="网络服务重启成功"
            )

        except Exception as e:
            logger.error(f"重启网络服务失败: {e}")
            return NetworkOperationResponse(
                success=False,
                message=f"重启网络服务失败: {str(e)}"
            )
