# backend/app/services/press_service/upgrade_service.py
import os
import zipfile
import asyncio
from datetime import datetime
from typing import List, Tuple, Dict, Any
from ...models.press.upgrade import (
    UpgradeType, UpgradePackageInfo, UpgradeRequest, UpgradeResponse,
    ValidationRequest, ValidationResponse, UpgradeStep, UpgradeStatus
)
from ..ssh_service import session_manager
from .process_service import ProcessService
from .backup_service import BackupService
from ...utils.logger import get_logger

logger = get_logger(__name__)

class UpgradeService:
    """升级管理服务类"""

    UPGRADE_BASE_DIR = "/userdata/upgrade"
    PRESS_CONTROL_DIR = "/root/PressControl"
    ROOT_DIR = "/root"

    # 全局升级状态存储（实际项目中应使用Redis或数据库）
    _upgrade_status: Dict[str, UpgradeStatus] = {}

    @staticmethod
    async def ensure_upgrade_directory(session_id: str):
        """确保升级目录存在"""
        try:
            # 创建升级基础目录和子目录
            commands = [
                f"mkdir -p {UpgradeService.UPGRADE_BASE_DIR}",
                f"mkdir -p {UpgradeService.UPGRADE_BASE_DIR}/PressControl",
                f"mkdir -p {UpgradeService.UPGRADE_BASE_DIR}/Database"
            ]

            for command in commands:
                await session_manager.execute_command(session_id, command)

            logger.info(f"确保升级目录存在: {UpgradeService.UPGRADE_BASE_DIR}")
        except Exception as e:
            logger.error(f"创建升级目录失败: {e}")
            raise

    @staticmethod
    async def validate_package(session_id: str, package_path: str, upgrade_type: UpgradeType) -> ValidationResponse:
        """验证升级包"""
        logger.info(f"开始验证升级包 - 类型: {upgrade_type.value}, 路径: {package_path}")

        try:
            # 获取SSH连接并下载文件进行本地验证
            connection = session_manager.get_connection(session_id)
            if not connection:
                return ValidationResponse(
                    success=False,
                    message="SSH连接不存在",
                    required_files=[],
                    found_files=[],
                    missing_files=[]
                )

            # 使用SFTP读取远程压缩包内容进行验证
            async with connection.start_sftp_client() as sftp:
                # 检查文件是否存在
                try:
                    stat_info = await sftp.stat(package_path)
                    if stat_info.size == 0:
                        return ValidationResponse(
                            success=False,
                            message="上传的文件为空",
                            required_files=[],
                            found_files=[],
                            missing_files=[]
                        )
                except:
                    return ValidationResponse(
                        success=False,
                        message="升级包文件不存在",
                        required_files=[],
                        found_files=[],
                        missing_files=[]
                    )

                # 读取压缩包内容
                async with sftp.open(package_path, 'rb') as remote_file:
                    file_content = await remote_file.read()

                # 写入临时文件进行验证
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
                    temp_file.write(file_content)
                    temp_path = temp_file.name

                try:
                    # 验证ZIP文件
                    with zipfile.ZipFile(temp_path, 'r') as zip_file:
                        found_files = zip_file.namelist()

                        if upgrade_type == UpgradeType.PRESS_CONTROL:
                            required_files = ["rtPress", "Lib/libCore.so", "Lib/libsqliteapi.so"]
                        elif upgrade_type == UpgradeType.DATABASE:
                            required_files = ["press.lx", "press.lx-shm", "press.lx-wal"]
                        else:
                            return ValidationResponse(
                                success=False,
                                message=f"不支持的升级类型: {upgrade_type.value}",
                                required_files=[],
                                found_files=[],
                                missing_files=[]
                            )

                        # 检查必需文件
                        missing_files = [f for f in required_files if f not in found_files]

                        if missing_files:
                            logger.warning(f"升级包缺少文件: {missing_files}")
                            return ValidationResponse(
                                success=False,
                                message=f"升级包缺少必需文件: {', '.join(missing_files)}",
                                required_files=required_files,
                                found_files=found_files,
                                missing_files=missing_files
                            )

                        # 额外验证
                        if upgrade_type == UpgradeType.PRESS_CONTROL:
                            # 验证rtPress文件大小
                            rtpress_info = zip_file.getinfo("rtPress")
                            if rtpress_info.file_size == 0:
                                return ValidationResponse(
                                    success=False,
                                    message="rtPress文件为空",
                                    required_files=required_files,
                                    found_files=found_files,
                                    missing_files=["rtPress (文件为空)"]
                                )

                        logger.info(f"升级包验证成功 - 类型: {upgrade_type.value}")
                        return ValidationResponse(
                            success=True,
                            message="升级包验证通过",
                            required_files=required_files,
                            found_files=found_files,
                            missing_files=[]
                        )

                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        except zipfile.BadZipFile:
            logger.error("无效的ZIP文件格式")
            return ValidationResponse(
                success=False,
                message="无效的ZIP文件格式",
                required_files=[],
                found_files=[],
                missing_files=[]
            )
        except Exception as e:
            logger.error(f"验证升级包失败: {e}")
            return ValidationResponse(
                success=False,
                message=f"验证失败: {str(e)}",
                required_files=[],
                found_files=[],
                missing_files=[]
            )

    @staticmethod
    async def execute_upgrade(session_id: str, upgrade_type: UpgradeType) -> UpgradeResponse:
        """执行升级流程"""
        upgrade_id = f"{session_id}_{upgrade_type.value}_{int(datetime.now().timestamp())}"
        logger.info(f"开始执行升级 - ID: {upgrade_id}, 类型: {upgrade_type.value}")

        # 初始化升级状态
        steps = [
            UpgradeStep(step_name="初始化", is_completed=False, message="准备开始升级", timestamp=""),
            UpgradeStep(step_name="停止程序", is_completed=False, message="停止rtPress进程", timestamp=""),
            UpgradeStep(step_name="创建备份", is_completed=False, message="备份当前版本", timestamp=""),
            UpgradeStep(step_name="解压文件", is_completed=False, message="解压升级包", timestamp=""),
            UpgradeStep(step_name="安装文件", is_completed=False, message="安装新版本文件", timestamp=""),
            UpgradeStep(step_name="设置权限", is_completed=False, message="设置文件权限", timestamp=""),
            UpgradeStep(step_name="启动程序", is_completed=False, message="启动rtPress进程", timestamp=""),
            UpgradeStep(step_name="验证安装", is_completed=False, message="验证升级结果", timestamp="")
        ]

        status = UpgradeStatus(
            is_upgrading=True,
            current_step="初始化",
            progress=0,
            steps=steps,
            log_messages=[],
            start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            success=None
        )

        UpgradeService._upgrade_status[session_id] = status

        # 异步执行升级流程
        logger.info(f"创建异步升级任务 - Session: {session_id}, Type: {upgrade_type.value}")
        task = asyncio.create_task(UpgradeService._execute_upgrade_async(session_id, upgrade_type, upgrade_id))

        # 添加任务完成回调，用于调试
        def task_done_callback(task_result):
            try:
                if task_result.exception():
                    logger.error(f"异步升级任务异常: {task_result.exception()}")
                else:
                    logger.info(f"异步升级任务完成 - Session: {session_id}")
            except Exception as e:
                logger.error(f"任务回调处理异常: {e}")

        task.add_done_callback(task_done_callback)

        return UpgradeResponse(
            success=True,
            message="升级任务已启动",
            upgrade_id=upgrade_id
        )

    @staticmethod
    async def _execute_upgrade_async(session_id: str, upgrade_type: UpgradeType, upgrade_id: str):
        """异步执行升级流程"""
        logger.info(f"异步升级任务开始执行 - Session: {session_id}, Type: {upgrade_type.value}, ID: {upgrade_id}")

        try:
            status = UpgradeService._upgrade_status[session_id]
            logger.info(f"获取到升级状态对象 - is_upgrading: {status.is_upgrading}")
        except KeyError:
            logger.error(f"无法找到升级状态 - Session: {session_id}")
            return

        try:
            # 步骤1: 初始化
            await UpgradeService._update_step(session_id, 0, True, "升级初始化完成")
            await UpgradeService._add_log(session_id, f"开始升级 {upgrade_type.value}")

            # 步骤2: 停止程序
            status.current_step = "停止程序"
            status.progress = 12
            stop_success, stop_message = await ProcessService.stop_rtpress(session_id)
            await UpgradeService._update_step(session_id, 1, stop_success, stop_message)
            await UpgradeService._add_log(session_id, stop_message)

            if not stop_success:
                raise Exception(stop_message)

            # 步骤3: 创建备份
            status.current_step = "创建备份"
            status.progress = 25
            backup_success, backup_message, backup_path = await BackupService.create_backup(session_id, upgrade_type)
            await UpgradeService._update_step(session_id, 2, backup_success, backup_message)
            await UpgradeService._add_log(session_id, backup_message)

            if not backup_success:
                raise Exception(backup_message)

            # 步骤4: 解压文件
            status.current_step = "解压文件"
            status.progress = 37
            extract_success, extract_message = await UpgradeService._extract_package(session_id, upgrade_type)
            await UpgradeService._update_step(session_id, 3, extract_success, extract_message)
            await UpgradeService._add_log(session_id, extract_message)

            if not extract_success:
                raise Exception(extract_message)

            # 步骤5: 安装文件
            status.current_step = "安装文件"
            status.progress = 50
            install_success, install_message = await UpgradeService._install_files(session_id, upgrade_type)
            await UpgradeService._update_step(session_id, 4, install_success, install_message)
            await UpgradeService._add_log(session_id, install_message)

            if not install_success:
                raise Exception(install_message)

            # 步骤6: 设置权限
            status.current_step = "设置权限"
            status.progress = 62
            chmod_success, chmod_message = await UpgradeService._set_permissions(session_id, upgrade_type)
            await UpgradeService._update_step(session_id, 5, chmod_success, chmod_message)
            await UpgradeService._add_log(session_id, chmod_message)

            if not chmod_success:
                raise Exception(chmod_message)

            # 步骤7: 启动程序
            status.current_step = "启动程序"
            status.progress = 75
            start_success, start_message = await ProcessService.start_rtpress(session_id)
            await UpgradeService._update_step(session_id, 6, start_success, start_message)
            await UpgradeService._add_log(session_id, start_message)

            if not start_success:
                raise Exception(start_message)

            # 步骤8: 验证安装
            status.current_step = "验证安装"
            status.progress = 87
            verify_success, verify_message = await UpgradeService._verify_installation(session_id)
            await UpgradeService._update_step(session_id, 7, verify_success, verify_message)
            await UpgradeService._add_log(session_id, verify_message)

            # 升级完成
            status.is_upgrading = False
            status.progress = 100
            status.success = True
            status.current_step = "升级完成"
            status.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await UpgradeService._add_log(session_id, f"{upgrade_type.value} 升级成功完成！")

            logger.info(f"升级成功完成 - ID: {upgrade_id}")

        except Exception as e:
            # 升级失败
            status.is_upgrading = False
            status.success = False
            status.current_step = "升级失败"
            status.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await UpgradeService._add_log(session_id, f"升级失败: {str(e)}")

            logger.error(f"升级失败 - ID: {upgrade_id}, 错误: {e}")

    @staticmethod
    async def _extract_package(session_id: str, upgrade_type: UpgradeType) -> Tuple[bool, str]:
        """解压升级包"""
        try:
            package_path = f"{UpgradeService.UPGRADE_BASE_DIR}/{upgrade_type.value}_package.zip"
            extract_path = f"{UpgradeService.UPGRADE_BASE_DIR}/{upgrade_type.value}"

            # 清理目标目录
            clean_command = f"rm -rf {extract_path}/*"
            await session_manager.execute_command(session_id, clean_command)

            # 解压ZIP文件
            unzip_command = f"cd {extract_path} && unzip -o {package_path}"
            await session_manager.execute_command(session_id, unzip_command)

            logger.info(f"升级包解压成功: {package_path}")
            return True, "升级包解压成功"

        except Exception as e:
            logger.error(f"解压升级包失败: {e}")
            return False, f"解压失败: {str(e)}"

    @staticmethod
    async def _install_files(session_id: str, upgrade_type: UpgradeType) -> Tuple[bool, str]:
        """安装文件"""
        try:
            source_path = f"{UpgradeService.UPGRADE_BASE_DIR}/{upgrade_type.value}"

            if upgrade_type == UpgradeType.PRESS_CONTROL:
                # 删除旧的PressControl目录
                remove_command = f"rm -rf {UpgradeService.PRESS_CONTROL_DIR}"
                await session_manager.execute_command(session_id, remove_command)

                # 创建新的PressControl目录
                mkdir_command = f"mkdir -p {UpgradeService.PRESS_CONTROL_DIR}"
                await session_manager.execute_command(session_id, mkdir_command)

                # 复制新文件
                copy_command = f"cp -r {source_path}/* {UpgradeService.PRESS_CONTROL_DIR}/"
                await session_manager.execute_command(session_id, copy_command)

                logger.info("下位机文件安装成功")
                return True, "下位机文件安装成功"

            elif upgrade_type == UpgradeType.DATABASE:
                # 删除旧的数据库文件
                db_files = ["press.lx", "press.lx-shm", "press.lx-wal"]
                for db_file in db_files:
                    remove_command = f"rm -f {UpgradeService.ROOT_DIR}/{db_file}"
                    await session_manager.execute_command(session_id, remove_command)

                # 复制新的数据库文件
                copy_command = f"cp {source_path}/press.lx* {UpgradeService.ROOT_DIR}/"
                await session_manager.execute_command(session_id, copy_command)

                logger.info("数据库文件安装成功")
                return True, "数据库文件安装成功"

        except Exception as e:
            logger.error(f"安装文件失败: {e}")
            return False, f"安装文件失败: {str(e)}"

    @staticmethod
    async def _set_permissions(session_id: str, upgrade_type: UpgradeType) -> Tuple[bool, str]:
        """设置文件权限"""
        try:
            if upgrade_type == UpgradeType.PRESS_CONTROL:
                chmod_command = f"chmod -R 777 {UpgradeService.PRESS_CONTROL_DIR}"
                await session_manager.execute_command(session_id, chmod_command)
                return True, "下位机文件权限设置成功"

            elif upgrade_type == UpgradeType.DATABASE:
                chmod_command = f"chmod 777 {UpgradeService.ROOT_DIR}/press.lx*"
                await session_manager.execute_command(session_id, chmod_command)
                return True, "数据库文件权限设置成功"

        except Exception as e:
            logger.error(f"设置权限失败: {e}")
            return False, f"设置权限失败: {str(e)}"

    @staticmethod
    async def _verify_installation(session_id: str) -> Tuple[bool, str]:
        """验证安装"""
        try:
            logger.info("开始验证安装 - 等待程序完全启动...")
            # 等待一段时间让程序完全启动
            await asyncio.sleep(3)

            logger.info("检查rtPress进程状态...")
            # 检查rtPress进程状态
            is_running, process_count, message = await ProcessService.check_rtpress_status(session_id)
            logger.info(f"进程状态检查结果: is_running={is_running}, count={process_count}, message={message}")

            if is_running:
                logger.info(f"验证成功，rtPress正在运行 (进程数: {process_count})")
                return True, f"验证成功，rtPress正在运行 (进程数: {process_count})"
            else:
                logger.warning(f"验证失败，rtPress未能正常启动: {message}")
                return False, f"验证失败，rtPress未能正常启动: {message}"

        except Exception as e:
            logger.error(f"验证安装失败: {e}")
            return False, f"验证失败: {str(e)}"

    @staticmethod
    async def _update_step(session_id: str, step_index: int, is_completed: bool, message: str):
        """更新升级步骤状态"""
        if session_id in UpgradeService._upgrade_status:
            status = UpgradeService._upgrade_status[session_id]
            if 0 <= step_index < len(status.steps):
                status.steps[step_index].is_completed = is_completed
                status.steps[step_index].message = message
                status.steps[step_index].timestamp = datetime.now().strftime("%H:%M:%S")

    @staticmethod
    async def _add_log(session_id: str, message: str):
        """添加日志消息"""
        if session_id in UpgradeService._upgrade_status:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}"
            UpgradeService._upgrade_status[session_id].log_messages.append(log_message)

            # 限制日志数量，避免内存溢出
            if len(UpgradeService._upgrade_status[session_id].log_messages) > 100:
                UpgradeService._upgrade_status[session_id].log_messages.pop(0)

    @staticmethod
    async def get_upgrade_status(session_id: str) -> UpgradeStatus:
        """获取升级状态"""
        if session_id in UpgradeService._upgrade_status:
            return UpgradeService._upgrade_status[session_id]
        else:
            # 返回默认状态
            return UpgradeStatus(
                is_upgrading=False,
                current_step="未开始",
                progress=0,
                steps=[],
                log_messages=["暂无升级任务"]
            )
