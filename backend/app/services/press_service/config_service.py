# backend/app/services/press_service/config_service.py
from typing import <PERSON>ple
from ...models.press.config import SDOReadRequest, SDOReadResponse, SDOWriteRequest, SDOWriteResponse
from ...utils.database import DatabaseManager
from ...utils.sdo_config import SDOConfigManager
from .process_service import ProcessService
from ..ssh_service import session_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

class ConfigService:
    """压机配置服务类"""

    @staticmethod
    async def read_sdo_config(session_id: str) -> SDOReadResponse:
        """
        读取SDO配置

        Args:
            session_id: 会话ID

        Returns:
            SDOReadResponse: 读取结果
        """
        try:
            logger.info(f"开始读取SDO配置 - Session: {session_id}")

            # 1. 检查数据库连接
            if not await DatabaseManager.check_database_connection(session_id):
                raise Exception("数据库连接失败")

            # 2. 读取数据库中的SDO内容
            content = await DatabaseManager.read_sdo_content(session_id)

            if not content.strip():
                raise Exception("数据库中未找到SDO配置内容")

            # 3. 解析为配置对象
            config = SDOConfigManager.parse_sdo_content(content)

            logger.info(f"SDO配置读取成功 - Session: {session_id}")
            return SDOReadResponse(
                success=True,
                message="SDO配置读取成功",
                config=config
            )

        except Exception as e:
            error_msg = f"读取SDO配置失败: {str(e)}"
            logger.error(f"{error_msg} - Session: {session_id}")
            return SDOReadResponse(
                success=False,
                message=error_msg,
                config=None
            )

    @staticmethod
    async def write_sdo_config(request: SDOWriteRequest) -> SDOWriteResponse:
        """
        写入SDO配置（包含进程管理）

        Args:
            request: 写入请求

        Returns:
            SDOWriteResponse: 写入结果
        """
        try:
            logger.info(f"开始写入SDO配置 - Session: {request.session_id}")

            # 1. 检查数据库连接
            if not await DatabaseManager.check_database_connection(request.session_id):
                raise Exception("数据库连接失败")

            # 2. 停止rtPress进程
            logger.info("正在停止rtPress进程...")
            stop_success, stop_msg = await ProcessService.stop_rtpress(request.session_id)
            if not stop_success:
                raise Exception(f"停止rtPress进程失败: {stop_msg}")
            logger.info(f"rtPress进程已停止: {stop_msg}")

            # 3. 读取原始内容
            original_content = await DatabaseManager.read_sdo_content(request.session_id)

            if not original_content.strip():
                raise Exception("数据库中未找到SDO配置内容")

            # 4. 序列化新配置
            new_content = SDOConfigManager.serialize_sdo_config(request.config, original_content)

            # 5. 写入数据库（确保事务完整性）
            success = await DatabaseManager.write_sdo_content(request.session_id, new_content)

            if not success:
                raise Exception("写入数据库失败")

            logger.info("SDO配置已成功写入数据库")

            # 6. 启动rtPress进程
            logger.info("正在启动rtPress进程...")
            start_success, start_msg = await ProcessService.start_rtpress(request.session_id)
            if not start_success:
                logger.warning(f"启动rtPress进程失败: {start_msg}")
                return SDOWriteResponse(
                    success=False,
                    message=f"数据库写入成功，但启动rtPress进程失败: {start_msg}"
                )

            logger.info(f"rtPress进程已启动: {start_msg}")
            logger.info(f"SDO配置写入完成 - Session: {request.session_id}")

            return SDOWriteResponse(
                success=True,
                message="SDO配置写入成功，rtPress进程已重启"
            )

        except Exception as e:
            error_msg = f"写入SDO配置失败: {str(e)}"
            logger.error(f"{error_msg} - Session: {request.session_id}")

            # 发生异常时尝试重启进程
            try:
                logger.info("异常恢复：尝试启动rtPress进程...")
                start_success, start_msg = await ProcessService.start_rtpress(request.session_id)
                if start_success:
                    logger.info(f"异常恢复成功：rtPress进程已启动: {start_msg}")
                else:
                    logger.error(f"异常恢复失败：无法启动rtPress进程: {start_msg}")
            except Exception as recovery_e:
                logger.error(f"异常恢复过程中发生错误: {recovery_e}")

            return SDOWriteResponse(
                success=False,
                message=error_msg
            )

    @staticmethod
    async def backup_database(session_id: str) -> dict:
        """
        备份数据库

        Args:
            session_id: 会话ID

        Returns:
            dict: 备份结果
        """
        try:
            logger.info(f"开始备份数据库 - Session: {session_id}")

            # 生成备份路径（带时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"/userdata/backup/press_{timestamp}.lx"

            # 创建备份目录
            mkdir_cmd = "mkdir -p /userdata/backup"
            await session_manager.execute_command(session_id, mkdir_cmd)

            # 复制数据库文件
            copy_cmd = f"cp {DatabaseManager.REMOTE_DATABASE_PATH} {backup_path}"
            result = await session_manager.execute_command(session_id, copy_cmd)

            # 检查备份是否成功
            check_cmd = f"ls -la {backup_path}"
            check_result = await session_manager.execute_command(session_id, check_cmd)

            if "press_" in check_result:
                logger.info(f"数据库备份成功: {backup_path}")
                return {
                    'success': True,
                    'message': '数据库备份成功',
                    'backup_path': backup_path
                }
            else:
                raise Exception("备份文件验证失败")

        except Exception as e:
            error_msg = f"数据库备份失败: {str(e)}"
            logger.error(f"{error_msg} - Session: {session_id}")
            return {
                'success': False,
                'message': error_msg
            }

    @staticmethod
    async def restore_database(session_id: str, backup_path: str) -> dict:
        """
        恢复数据库

        Args:
            session_id: 会话ID
            backup_path: 备份文件路径

        Returns:
            dict: 恢复结果
        """
        try:
            logger.info(f"开始恢复数据库 - Session: {session_id}, 备份文件: {backup_path}")

            # 检查备份文件是否存在
            check_cmd = f"ls -la {backup_path}"
            check_result = await session_manager.execute_command(session_id, check_cmd)

            if "No such file" in check_result:
                raise Exception(f"备份文件不存在: {backup_path}")

            # 停止rtPress进程
            logger.info("正在停止rtPress进程...")
            stop_success, stop_msg = await ProcessService.stop_rtpress(session_id)
            if not stop_success:
                raise Exception(f"停止rtPress进程失败: {stop_msg}")

            # 备份当前数据库
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            current_backup = f"/userdata/backup/press_before_restore_{timestamp}.lx"
            backup_current_cmd = f"cp {DatabaseManager.REMOTE_DATABASE_PATH} {current_backup}"
            await session_manager.execute_command(session_id, backup_current_cmd)
            logger.info(f"当前数据库已备份至: {current_backup}")

            # 恢复数据库
            restore_cmd = f"cp {backup_path} {DatabaseManager.REMOTE_DATABASE_PATH}"
            await session_manager.execute_command(session_id, restore_cmd)

            # 设置文件权限
            chmod_cmd = f"chmod 777 {DatabaseManager.REMOTE_DATABASE_PATH}"
            await session_manager.execute_command(session_id, chmod_cmd)

            # 启动rtPress进程
            logger.info("正在启动rtPress进程...")
            start_success, start_msg = await ProcessService.start_rtpress(session_id)
            if not start_success:
                logger.warning(f"启动rtPress进程失败: {start_msg}")
                return {
                    'success': False,
                    'message': f"数据库恢复成功，但启动rtPress进程失败: {start_msg}"
                }

            logger.info(f"数据库恢复完成 - Session: {session_id}")
            return {
                'success': True,
                'message': '数据库恢复成功，rtPress进程已重启'
            }

        except Exception as e:
            error_msg = f"数据库恢复失败: {str(e)}"
            logger.error(f"{error_msg} - Session: {session_id}")

            # 发生异常时尝试重启进程
            try:
                logger.info("异常恢复：尝试启动rtPress进程...")
                start_success, start_msg = await ProcessService.start_rtpress(session_id)
                if start_success:
                    logger.info(f"异常恢复成功：rtPress进程已启动: {start_msg}")
                else:
                    logger.error(f"异常恢复失败：无法启动rtPress进程: {start_msg}")
            except Exception as recovery_e:
                logger.error(f"异常恢复过程中发生错误: {recovery_e}")

            return {
                'success': False,
                'message': error_msg
            }
