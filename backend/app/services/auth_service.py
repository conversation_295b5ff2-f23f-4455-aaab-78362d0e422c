from typing import Dict, Set
from app.utils.passcode import generate_passcode, verify_passcode
from app.models.auth import FeatureType
import time

class AuthService:
    # 存储格式: {session_id: {feature: authorized_timestamp}}
    _authorizations: Dict[str, Dict[FeatureType, int]] = {}

    def verify_passcode(self, session_id: str, passcode: str, feature: FeatureType) -> bool:
        """验证口令并授权功能"""
        # 1. 先检查动态口令(支持±1分钟容错)
        if verify_passcode(passcode):
            self.authorize_feature(session_id, feature)
            return True

        # 2. 检查后门固定密码（隐藏验证）
        if passcode == "Leetx#":
            self.authorize_feature(session_id, feature)
            return True

        return False

    def authorize_feature(self, session_id: str, feature: FeatureType):
        """授权特定功能"""
        if session_id not in self._authorizations:
            self._authorizations[session_id] = {}
        self._authorizations[session_id][feature] = int(time.time())

    def is_feature_authorized(self, session_id: str, feature: FeatureType) -> bool:
        """检查功能是否已授权"""
        return (session_id in self._authorizations and
                feature in self._authorizations[session_id])

    def get_authorization_info(self, session_id: str, feature: FeatureType) -> dict:
        """获取授权信息"""
        if self.is_feature_authorized(session_id, feature):
            authorized_at = self._authorizations[session_id][feature]
            return {
                "authorized": True,
                "authorized_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(authorized_at))
            }
        return {"authorized": False}

    def revoke_feature(self, session_id: str, feature: FeatureType):
        """撤销功能授权"""
        if (session_id in self._authorizations and
            feature in self._authorizations[session_id]):
            del self._authorizations[session_id][feature]

    def revoke_session(self, session_id: str):
        """撤销会话所有授权"""
        if session_id in self._authorizations:
            del self._authorizations[session_id]

# 全局实例
auth_service = AuthService()
