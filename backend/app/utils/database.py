# backend/app/utils/database.py
import base64
from typing import Optional
from ..services.ssh_service import session_manager
from .logger import get_logger

logger = get_logger(__name__)

class DatabaseManager:
    """数据库管理类 - 通过SSH远程Python操作数据库"""

    REMOTE_DATABASE_PATH = "/root/press.lx"

    @staticmethod
    async def read_sdo_content(session_id: str) -> str:
        """
        通过SSH远程Python读取SDO内容

        Args:
            session_id: SSH会话ID

        Returns:
            str: SDO内容

        Raises:
            Exception: 数据库操作失败
        """
        try:
            logger.info(f"开始通过远程Python读取SDO内容 - Session: {session_id}")

            # 构造远程Python代码
            python_code = f'''
import sqlite3
import sys
try:
    conn = sqlite3.connect('{DatabaseManager.REMOTE_DATABASE_PATH}')
    cursor = conn.execute('SELECT Content FROM Sdo WHERE Name=? AND Version=?', ('Sdo', 'V1.0.0'))
    result = cursor.fetchone()
    if result and result[0]:
        print(result[0], end='')
    else:
        print('NO_DATA_FOUND', end='')
    conn.close()
except Exception as e:
    print('ERROR: ' + str(e), file=sys.stderr)
    sys.exit(1)
'''

            # 执行远程Python命令
            ssh_command = f"python3 -c \"{python_code.replace('\"', '\\\"')}\""
            logger.debug(f"执行远程Python命令")

            result = await session_manager.execute_command(session_id, ssh_command)

            if not result or result.strip() == 'NO_DATA_FOUND':
                logger.warning("远程数据库中未找到SDO记录")
                return ""

            content = result
            logger.info(f"远程SDO内容读取成功，内容长度: {len(content)} 字符")

            return content

        except Exception as e:
            logger.error(f"读取远程SDO内容失败: {e}")
            raise Exception(f"读取远程数据库失败: {str(e)}")

    @staticmethod
    async def write_sdo_content(session_id: str, content: str) -> bool:
        """
        通过SSH远程Python写入SDO内容

        Args:
            session_id: SSH会话ID
            content: 完整的SDO内容

        Returns:
            bool: 写入是否成功

        Raises:
            Exception: 数据库操作失败
        """
        try:
            logger.info(f"开始通过远程Python写入SDO内容 - Session: {session_id}, 内容长度: {len(content)} 字符")

            # 方法：使用临时文件安全传输内容
            temp_file = "/tmp/sdo_content_update.txt"

            # 步骤1: 将内容写入远程临时文件
            # 使用 cat > file << 'EOF' 方式避免特殊字符问题
            create_temp_cmd = f'''cat > {temp_file} << 'SDOEOF'
{content}
SDOEOF'''

            logger.debug("正在创建远程临时文件...")
            await session_manager.execute_command(session_id, create_temp_cmd)

            # 步骤2: 使用Python读取临时文件并更新数据库
            python_code = f'''
import sqlite3
import sys
try:
    # 读取临时文件内容
    with open('{temp_file}', 'r', encoding='utf-8') as f:
        content = f.read()

    # 连接数据库并更新
    conn = sqlite3.connect('{DatabaseManager.REMOTE_DATABASE_PATH}')
    cursor = conn.execute('UPDATE Sdo SET Content=? WHERE Name=? AND Version=?', (content, 'Sdo', 'V1.0.0'))
    rows_affected = cursor.rowcount

    if rows_affected > 0:
        conn.commit()
        print('SUCCESS:' + str(rows_affected), end='')
    else:
        print('NO_ROWS_UPDATED', end='')

    conn.close()
except Exception as e:
    print('ERROR: ' + str(e), file=sys.stderr)
    sys.exit(1)
'''

            # 执行远程Python更新命令
            update_command = f"python3 -c \"{python_code.replace('\"', '\\\"')}\""
            logger.debug("正在执行远程Python数据库更新...")

            update_result = await session_manager.execute_command(session_id, update_command)

            # 步骤3: 清理临时文件
            cleanup_cmd = f"rm -f {temp_file}"
            await session_manager.execute_command(session_id, cleanup_cmd)
            logger.debug("已清理远程临时文件")

            # 检查更新结果
            if update_result.startswith('SUCCESS:'):
                rows_affected = update_result.split(':')[1]
                logger.info(f"远程SDO内容写入成功 - 影响行数: {rows_affected}")
                return True
            elif update_result == 'NO_ROWS_UPDATED':
                logger.warning("未找到需要更新的SDO记录")
                return False
            else:
                logger.error(f"更新失败: {update_result}")
                return False

        except Exception as e:
            logger.error(f"写入远程SDO内容失败: {e}")
            # 确保清理临时文件
            try:
                cleanup_cmd = f"rm -f {temp_file}"
                await session_manager.execute_command(session_id, cleanup_cmd)
            except:
                pass
            raise Exception(f"写入远程数据库失败: {str(e)}")

    @staticmethod
    async def check_database_connection(session_id: str) -> bool:
        """
        检查远程数据库连接是否正常

        Args:
            session_id: SSH会话ID

        Returns:
            bool: 连接是否正常
        """
        try:
            logger.debug(f"开始检查远程数据库连接 - Session: {session_id}")

            # 构造检查Python代码
            python_code = f'''
import sqlite3
import os
import sys
try:
    # 检查文件是否存在
    if not os.path.exists('{DatabaseManager.REMOTE_DATABASE_PATH}'):
        print('DATABASE_NOT_FOUND', end='')
        sys.exit(0)

    # 测试数据库连接和查询
    conn = sqlite3.connect('{DatabaseManager.REMOTE_DATABASE_PATH}')
    cursor = conn.execute('SELECT COUNT(*) FROM Sdo WHERE Name=? AND Version=?', ('Sdo', 'V1.0.0'))
    count = cursor.fetchone()[0]
    print('RECORD_COUNT:' + str(count), end='')
    conn.close()
except Exception as e:
    print('ERROR: ' + str(e), file=sys.stderr)
    sys.exit(1)
'''

            # 执行远程检查命令
            check_command = f"python3 -c \"{python_code.replace('\"', '\\\"')}\""
            result = await session_manager.execute_command(session_id, check_command)

            if result == 'DATABASE_NOT_FOUND':
                logger.error(f"远程数据库文件不存在: {DatabaseManager.REMOTE_DATABASE_PATH}")
                return False
            elif result.startswith('RECORD_COUNT:'):
                count = int(result.split(':')[1])
                logger.info(f"远程数据库连接正常，SDO记录数: {count}")
                return count > 0
            else:
                logger.error(f"数据库检查异常: {result}")
                return False

        except Exception as e:
            logger.error(f"远程数据库连接检查失败: {e}")
            return False
