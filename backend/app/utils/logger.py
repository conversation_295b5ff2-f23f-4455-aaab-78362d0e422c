import logging
import sys
from pathlib import Path

def setup_logger():
    """配置全局日志系统"""
    # 确保logs目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 全局日志配置
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            # 文件日志处理器
            logging.FileHandler('logs/ControllerTool.log', encoding='utf-8'),
            # 控制台日志处理器
            logging.StreamHandler(sys.stdout)
        ]
    )

    # 设置第三方库日志级别
    logging.getLogger('asyncssh').setLevel(logging.WARNING)
    logging.getLogger('uvicorn').setLevel(logging.INFO)

def get_logger(name: str):
    """获取指定模块的logger实例"""
    return logging.getLogger(name)
