import hashlib
import time
from datetime import datetime

# 配置参数
SECRET = "lihongshen"  # 默认种子，可通过环境变量配置
LENGTH = 6

def generate_passcode(timestamp: int = None) -> str:
    """生成动态口令"""
    if timestamp is None:
        timestamp = int(time.time())

    # 精确到分钟
    minute = timestamp // 60

    # 生成原始字符串并计算哈希
    raw = f"{minute}:{SECRET}".encode()
    digest = hashlib.sha256(raw).hexdigest()[:8]  # 取前8个十六进制字符

    # 转为6位数字
    code = str(int(digest, 16) % (10**LENGTH)).zfill(LENGTH)
    return code

def verify_passcode(code: str, timestamp: int = None) -> bool:
    """验证动态口令，允许前一分钟、当前分钟和后一分钟的值"""
    if timestamp is None:
        timestamp = int(time.time())

    # 检查三个时间窗口的密钥
    for offset in (-60, 0, 60):  # 前一分钟、当前分钟、后一分钟
        if code == generate_passcode(timestamp + offset):
            return True
    return False

def get_passcode_info() -> dict:
    """获取当前口令和有效期信息"""
    current_time = int(time.time())
    current_minute = current_time // 60
    minute_end = (current_minute + 1) * 60
    remaining_seconds = minute_end - current_time

    return {
        "passcode": generate_passcode(current_time),
        "remaining_seconds": remaining_seconds,
        "expires_at": datetime.fromtimestamp(minute_end).strftime("%H:%M:%S")
    }
