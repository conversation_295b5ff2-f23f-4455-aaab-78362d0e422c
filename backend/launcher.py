# launcher.py
import os, sys, time, webbrowser, threading
from pathlib import Path

def get_resource_path(rel: str) -> str:
    """开发环境与 PyInstaller 环境通用的资源路径定位"""
    try:
        base = Path(sys._MEIPASS)            # 打包后临时目录
    except AttributeError:
        # 源码仓库根目录 (backend 的上一级)
        base = Path(__file__).resolve().parent.parent
    return str(base / rel)                   # 用 Path，避免反斜杠转义

def check_frontend_files() -> bool:
    frontend_dist = get_resource_path("frontend/dist")  # 用正斜杠
    index_file = Path(frontend_dist) / "index.html"
    if not index_file.exists():
        print("❌ 错误: 前端文件不存在！")
        print(f"查找路径: {frontend_dist}")
        print(f"index.html路径: {index_file}")
        return False
    return True


def main():
    print("=" * 50)
    print("🚀 ControllerTool 压机远程管理系统")
    print("=" * 50)

    # 检查前端文件
    if not check_frontend_files():
        input("按回车键退出...")
        return

    print("📡 正在启动服务器...")
    print("🌐 服务器地址: http://localhost:8000")
    print("📱 前端界面: http://localhost:8000")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)

    try:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open("http://localhost:8000")

        browser_thread = threading.Thread(target=open_browser)
        browser_thread.start()

        # 导入并启动 FastAPI 应用
        import uvicorn
        from app.main import app

        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
